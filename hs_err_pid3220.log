#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (ci/ciEnv.hpp:183), pid=3220, tid=17044
#  Error: ShouldNotReachHere()
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\255d4d824f24b3c8b7fd3c31bff27366\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\255d4d824f24b3c8b7fd3c31bff27366\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-5735a92c340cc2db1d98159711ca9564-sock

Host: Intel(R) Core(TM) i5-9300H CPU @ 2.40GHz, 8 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jun 10 07:36:25 2025 SE Asia Standard Time elapsed time: 12075.707485 seconds (0d 3h 21m 15s)

---------------  T H R E A D  ---------------

Current thread (0x0000023845051770):  JavaThread "C1 CompilerThread0" daemon [_thread_in_vm, id=17044, stack(0x000000a9a7f00000,0x000000a9a8000000) (1024K)]


Current CompileTask:
C1:12075707 18735   !   3       org.eclipse.jdt.ls.core.internal.JDTUtils::getFakeCompilationUnit (329 bytes)

Stack: [0x000000a9a7f00000,0x000000a9a8000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab067]
V  [jvm.dll+0x27f827]
V  [jvm.dll+0x27f84c]
V  [jvm.dll+0x27f7a0]
V  [jvm.dll+0x1eb84a]
V  [jvm.dll+0x1e7f61]
V  [jvm.dll+0x1e7e49]
V  [jvm.dll+0x2032f5]
V  [jvm.dll+0x1580fa]
V  [jvm.dll+0x15f2fa]
V  [jvm.dll+0x15d82c]
V  [jvm.dll+0x165f83]
V  [jvm.dll+0x1654a6]
V  [jvm.dll+0x15d088]
V  [jvm.dll+0x15f309]
V  [jvm.dll+0x15d925]
V  [jvm.dll+0x165f83]
V  [jvm.dll+0x1654a6]
V  [jvm.dll+0x15d088]
V  [jvm.dll+0x15f309]
V  [jvm.dll+0x15d925]
V  [jvm.dll+0x157857]
V  [jvm.dll+0x1670a6]
V  [jvm.dll+0x152cb0]
V  [jvm.dll+0x153033]
V  [jvm.dll+0x153304]
V  [jvm.dll+0x152736]
V  [jvm.dll+0x15450d]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000023847b20910, length=212, elements={
0x0000023865d7c8f0, 0x000002387a12cfc0, 0x000002387a12f3f0, 0x000002387a1313e0,
0x000002387a132030, 0x000002387a134b90, 0x000002387a1355e0, 0x000002387a136410,
0x0000023845051770, 0x0000023865de7a90, 0x00000238452a85a0, 0x0000023845b8c220,
0x0000023845c04360, 0x00000238459ae400, 0x00000238459aee30, 0x0000023845e06f80,
0x0000023845bb6c10, 0x000002384591c2f0, 0x0000023845918e70, 0x000002384591bc60,
0x0000023846579c50, 0x00000238465774f0, 0x00000238465788a0, 0x00000238465767d0,
0x0000023846578210, 0x0000023846578f30, 0x000002384657a2e0, 0x0000023846576e60,
0x000002384657d760, 0x000002384657d0d0, 0x000002384657ca40, 0x000002384657ddf0,
0x000002384aa50ba0, 0x000002384762b230, 0x000002384762b8c0, 0x000002384762bf50,
0x0000023847628ad0, 0x000002384762f3d0, 0x000002384762cc70, 0x000002384762d300,
0x000002384762e020, 0x000002384762d990, 0x000002385c8e4dd0, 0x000002385bf1fe90,
0x000002385bf21240, 0x000002385bf1eae0, 0x000002385bf1f800, 0x000002385bf1e450,
0x000002385bf22c80, 0x000002385bf239a0, 0x000002385bf23310, 0x000002385c8e5460,
0x000002385c6260e0, 0x000002385c627490, 0x000002385c6253c0, 0x000002385c6281b0,
0x000002385c626e00, 0x000002385c8e1fe0, 0x000002385c8df880, 0x000002385c8e2670,
0x000002385c8e05a0, 0x000002385c8e2d00, 0x000002385c8e3390, 0x000002385c8dff10,
0x000002385c8e40b0, 0x000002385c8e12c0, 0x000002385c8e5af0, 0x000002385c8e6810,
0x000002385c8e6ea0, 0x000002384aa4d090, 0x000002384aa4bce0, 0x000002384aa4c370,
0x000002384aa4fe80, 0x0000023847f5be20, 0x0000023847f5aa70, 0x0000023847f5def0,
0x0000023847f5c4b0, 0x0000023847f5d1d0, 0x0000023847f5cb40, 0x0000023847f5d860,
0x000002384aa4f7f0, 0x000002384aa4e440, 0x000002384591a220, 0x000002384591a8b0,
0x000002384591af40, 0x000002384762aba0, 0x00000238476297f0, 0x000002384762e6b0,
0x000002384762ed40, 0x000002385bf1f170, 0x000002385bf21f60, 0x000002384657a970,
0x0000023846577b80, 0x00000238465795c0, 0x000002384aa4d720, 0x000002385bf24d50,
0x000002384aa51230, 0x000002385bf225f0, 0x000002384591b5d0, 0x000002385ce3c690,
0x000002385ce3b970, 0x000002385ce3b2e0, 0x000002385ce3e0d0, 0x000002385ce3da40,
0x000002384aa50510, 0x000002385d06ee50, 0x000002385d06f4e0, 0x000002385d06c6f0,
0x000002385d06c060, 0x000002385d06daa0, 0x000002385d06cd80, 0x000002385d06d410,
0x000002385d06e130, 0x000002385ce3ac50, 0x0000023860219e70, 0x0000023860217da0,
0x0000023860218ac0, 0x0000023860219150, 0x00000238602197e0, 0x000002386021a500,
0x0000023860217080, 0x000002386021ab90, 0x000002386021b220, 0x0000023860217710,
0x000002386021b8b0, 0x0000023860218430, 0x000002386021cc60, 0x000002386021bf40,
0x000002386021c5d0, 0x000002386021d2f0, 0x000002386021d980, 0x000002386021e6a0,
0x000002385d06e7c0, 0x000002386021e010, 0x000002385bf24030, 0x000002384b18b6f0,
0x000002384b18bd80, 0x000002384b18d130, 0x000002384b18d7c0, 0x000002384b18de50,
0x000002384b18e4e0, 0x000002384b18b060, 0x000002384b18c410, 0x000002384b18caa0,
0x000002385e4ac380, 0x000002385e4aca10, 0x000002385e4addc0, 0x000002385e4ae450,
0x000002385e4ad0a0, 0x000002385e4aafd0, 0x000002385e4ad730, 0x000002385e4aeae0,
0x000002385e4af170, 0x000002385e4abcf0, 0x000002385e4afe90, 0x000002385e4b0520,
0x000002385e4af800, 0x000002385e4b4030, 0x000002385e4b0bb0, 0x000002385e4b1f60,
0x000002385e4b25f0, 0x000002385e4b46c0, 0x000002385e4b39a0, 0x000002385e4b18d0,
0x000002385e4b4d50, 0x000002385e4b2c80, 0x000002385e4b53e0, 0x000002385e4b3310,
0x000002385e4b8860, 0x000002385e4b8ef0, 0x000002385e4b6100, 0x000002385e4b5a70,
0x000002385e4b7b40, 0x000002385e4b9c10, 0x000002385e4b81d0, 0x000002385e4b74b0,
0x000002385e4b9580, 0x000002385e4ba2a0, 0x000002385e4b6790, 0x000002385e4b6e20,
0x000002385e4ba930, 0x000002385bf253e0, 0x000002385c8e6180, 0x000002385bf246c0,
0x000002384aa53300, 0x000002385ea785a0, 0x000002385ea76b60, 0x000002385ea77f10,
0x000002385ea757b0, 0x000002385ea77880, 0x000002385ea75e40, 0x000002385ea75120,
0x000002385ea792c0, 0x000002385ea78c30, 0x000002385ea764d0, 0x000002385ea7a670,
0x000002385ea7c0b0, 0x000002385ea771f0, 0x000002385ea7ba20, 0x000002385ea7cdd0,
0x000002385ea7d460, 0x000002385ea7daf0, 0x000002385ea7ad00, 0x000002385ea79fe0,
0x000002385ea7b390, 0x000002385ea7f530, 0x00000238617a3dd0, 0x00000238617a5810,
0x00000238617a78e0, 0x00000238617a7250, 0x00000238617a7f70, 0x00000238617a4af0
}

Java Threads: ( => current thread )
  0x0000023865d7c8f0 JavaThread "main"                              [_thread_blocked, id=3472, stack(0x000000a9a7400000,0x000000a9a7500000) (1024K)]
  0x000002387a12cfc0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7284, stack(0x000000a9a7800000,0x000000a9a7900000) (1024K)]
  0x000002387a12f3f0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=9000, stack(0x000000a9a7900000,0x000000a9a7a00000) (1024K)]
  0x000002387a1313e0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=6612, stack(0x000000a9a7a00000,0x000000a9a7b00000) (1024K)]
  0x000002387a132030 JavaThread "Attach Listener"            daemon [_thread_blocked, id=14976, stack(0x000000a9a7b00000,0x000000a9a7c00000) (1024K)]
  0x000002387a134b90 JavaThread "Service Thread"             daemon [_thread_blocked, id=13612, stack(0x000000a9a7c00000,0x000000a9a7d00000) (1024K)]
  0x000002387a1355e0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=13640, stack(0x000000a9a7d00000,0x000000a9a7e00000) (1024K)]
  0x000002387a136410 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=12784, stack(0x000000a9a7e00000,0x000000a9a7f00000) (1024K)]
=>0x0000023845051770 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=17044, stack(0x000000a9a7f00000,0x000000a9a8000000) (1024K)]
  0x0000023865de7a90 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=14580, stack(0x000000a9a8000000,0x000000a9a8100000) (1024K)]
  0x00000238452a85a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=3816, stack(0x000000a9a8100000,0x000000a9a8200000) (1024K)]
  0x0000023845b8c220 JavaThread "Active Thread: Equinox Container: 6295f932-9282-4925-a42c-762d5d6df50d"        [_thread_blocked, id=18144, stack(0x000000a9a8a00000,0x000000a9a8b00000) (1024K)]
  0x0000023845c04360 JavaThread "Refresh Thread: Equinox Container: 6295f932-9282-4925-a42c-762d5d6df50d" daemon [_thread_blocked, id=18008, stack(0x000000a9a8200000,0x000000a9a8300000) (1024K)]
  0x00000238459ae400 JavaThread "Framework Event Dispatcher: Equinox Container: 6295f932-9282-4925-a42c-762d5d6df50d" daemon [_thread_blocked, id=18000, stack(0x000000a9a8b00000,0x000000a9a8c00000) (1024K)]
  0x00000238459aee30 JavaThread "Start Level: Equinox Container: 6295f932-9282-4925-a42c-762d5d6df50d" daemon [_thread_blocked, id=17904, stack(0x000000a9a8c00000,0x000000a9a8d00000) (1024K)]
  0x0000023845e06f80 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=960, stack(0x000000a9a8e00000,0x000000a9a8f00000) (1024K)]
  0x0000023845bb6c10 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=18768, stack(0x000000a9a8d00000,0x000000a9a8e00000) (1024K)]
  0x000002384591c2f0 JavaThread "Worker-JM"                         [_thread_blocked, id=18856, stack(0x000000a9a9100000,0x000000a9a9200000) (1024K)]
  0x0000023845918e70 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=18952, stack(0x000000a9a9000000,0x000000a9a9100000) (1024K)]
  0x000002384591bc60 JavaThread "Java indexing"              daemon [_thread_blocked, id=18452, stack(0x000000a9a9700000,0x000000a9a9800000) (1024K)]
  0x0000023846579c50 JavaThread "Thread-2"                   daemon [_thread_in_native, id=18436, stack(0x000000a9a9d00000,0x000000a9a9e00000) (1024K)]
  0x00000238465774f0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=1668, stack(0x000000a9a9e00000,0x000000a9a9f00000) (1024K)]
  0x00000238465788a0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=18476, stack(0x000000a9a9f00000,0x000000a9aa000000) (1024K)]
  0x00000238465767d0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=17620, stack(0x000000a9aa000000,0x000000a9aa100000) (1024K)]
  0x0000023846578210 JavaThread "Thread-6"                   daemon [_thread_in_native, id=3648, stack(0x000000a9aa100000,0x000000a9aa200000) (1024K)]
  0x0000023846578f30 JavaThread "Thread-7"                   daemon [_thread_in_native, id=18508, stack(0x000000a9aa200000,0x000000a9aa300000) (1024K)]
  0x000002384657a2e0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=18488, stack(0x000000a9aa300000,0x000000a9aa400000) (1024K)]
  0x0000023846576e60 JavaThread "Thread-9"                   daemon [_thread_in_native, id=18480, stack(0x000000a9aa400000,0x000000a9aa500000) (1024K)]
  0x000002384657d760 JavaThread "Thread-10"                  daemon [_thread_in_native, id=18492, stack(0x000000a9aa500000,0x000000a9aa600000) (1024K)]
  0x000002384657d0d0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=2856, stack(0x000000a9aa600000,0x000000a9aa700000) (1024K)]
  0x000002384657ca40 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=18604, stack(0x000000a9aa700000,0x000000a9aa800000) (1024K)]
  0x000002384657ddf0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=18628, stack(0x000000a9aa800000,0x000000a9aa900000) (1024K)]
  0x000002384aa50ba0 JavaThread "Timer-0"                           [_thread_blocked, id=14912, stack(0x000000a9a7200000,0x000000a9a7300000) (1024K)]
  0x000002384762b230 JavaThread "Timer-1"                           [_thread_blocked, id=10424, stack(0x000000a9ab100000,0x000000a9ab200000) (1024K)]
  0x000002384762b8c0 JavaThread "Timer-2"                           [_thread_blocked, id=9096, stack(0x000000a9a9900000,0x000000a9a9a00000) (1024K)]
  0x000002384762bf50 JavaThread "Timer-3"                           [_thread_blocked, id=18624, stack(0x000000a9a9a00000,0x000000a9a9b00000) (1024K)]
  0x0000023847628ad0 JavaThread "Timer-4"                           [_thread_blocked, id=19368, stack(0x000000a9a7100000,0x000000a9a7200000) (1024K)]
  0x000002384762f3d0 JavaThread "Timer-5"                           [_thread_blocked, id=19324, stack(0x000000a9a9400000,0x000000a9a9500000) (1024K)]
  0x000002384762cc70 JavaThread "Timer-6"                           [_thread_blocked, id=18868, stack(0x000000a9aad00000,0x000000a9aae00000) (1024K)]
  0x000002384762d300 JavaThread "Timer-7"                           [_thread_blocked, id=2040, stack(0x000000a9a9600000,0x000000a9a9700000) (1024K)]
  0x000002384762e020 JavaThread "Timer-8"                           [_thread_blocked, id=19800, stack(0x000000a9a8f00000,0x000000a9a9000000) (1024K)]
  0x000002384762d990 JavaThread "Timer-9"                           [_thread_blocked, id=19768, stack(0x000000a9aae00000,0x000000a9aaf00000) (1024K)]
  0x000002385c8e4dd0 JavaThread "Timer-10"                          [_thread_blocked, id=19360, stack(0x000000a9ad600000,0x000000a9ad700000) (1024K)]
  0x000002385bf1fe90 JavaThread "Timer-11"                          [_thread_blocked, id=20076, stack(0x000000a9ad900000,0x000000a9ada00000) (1024K)]
  0x000002385bf21240 JavaThread "Timer-12"                          [_thread_blocked, id=20112, stack(0x000000a9ada00000,0x000000a9adb00000) (1024K)]
  0x000002385bf1eae0 JavaThread "Timer-13"                          [_thread_blocked, id=15152, stack(0x000000a9aaf00000,0x000000a9ab000000) (1024K)]
  0x000002385bf1f800 JavaThread "Timer-14"                          [_thread_blocked, id=7068, stack(0x000000a9ad500000,0x000000a9ad600000) (1024K)]
  0x000002385bf1e450 JavaThread "Timer-15"                          [_thread_blocked, id=13172, stack(0x000000a9ad700000,0x000000a9ad800000) (1024K)]
  0x000002385bf22c80 JavaThread "Timer-16"                          [_thread_blocked, id=7916, stack(0x000000a9adc00000,0x000000a9add00000) (1024K)]
  0x000002385bf239a0 JavaThread "Timer-17"                          [_thread_blocked, id=11132, stack(0x000000a9ade00000,0x000000a9adf00000) (1024K)]
  0x000002385bf23310 JavaThread "Timer-18"                          [_thread_blocked, id=11612, stack(0x000000a9add00000,0x000000a9ade00000) (1024K)]
  0x000002385c8e5460 JavaThread "Timer-19"                          [_thread_blocked, id=17976, stack(0x000000a9adf00000,0x000000a9ae000000) (1024K)]
  0x000002385c6260e0 JavaThread "Timer-20"                          [_thread_blocked, id=19008, stack(0x000000a9ae500000,0x000000a9ae600000) (1024K)]
  0x000002385c627490 JavaThread "Timer-21"                          [_thread_blocked, id=18720, stack(0x000000a9ae600000,0x000000a9ae700000) (1024K)]
  0x000002385c6253c0 JavaThread "Timer-22"                          [_thread_blocked, id=1596, stack(0x000000a9a7300000,0x000000a9a7400000) (1024K)]
  0x000002385c6281b0 JavaThread "Timer-23"                          [_thread_blocked, id=8724, stack(0x000000a9a9200000,0x000000a9a9300000) (1024K)]
  0x000002385c626e00 JavaThread "Timer-24"                          [_thread_blocked, id=8068, stack(0x000000a9a9300000,0x000000a9a9400000) (1024K)]
  0x000002385c8e1fe0 JavaThread "Timer-25"                          [_thread_blocked, id=9440, stack(0x000000a9aa900000,0x000000a9aaa00000) (1024K)]
  0x000002385c8df880 JavaThread "Timer-26"                          [_thread_blocked, id=19120, stack(0x000000a9ab200000,0x000000a9ab300000) (1024K)]
  0x000002385c8e2670 JavaThread "Timer-27"                          [_thread_blocked, id=13408, stack(0x000000a9ab000000,0x000000a9ab100000) (1024K)]
  0x000002385c8e05a0 JavaThread "Timer-28"                          [_thread_blocked, id=16596, stack(0x000000a9ab300000,0x000000a9ab400000) (1024K)]
  0x000002385c8e2d00 JavaThread "Timer-29"                          [_thread_blocked, id=18136, stack(0x000000a9ab400000,0x000000a9ab500000) (1024K)]
  0x000002385c8e3390 JavaThread "Timer-30"                          [_thread_blocked, id=1504, stack(0x000000a9ab700000,0x000000a9ab800000) (1024K)]
  0x000002385c8dff10 JavaThread "Timer-31"                          [_thread_blocked, id=13592, stack(0x000000a9a9c00000,0x000000a9a9d00000) (1024K)]
  0x000002385c8e40b0 JavaThread "Timer-32"                          [_thread_blocked, id=16008, stack(0x000000a9ab500000,0x000000a9ab600000) (1024K)]
  0x000002385c8e12c0 JavaThread "Timer-33"                          [_thread_blocked, id=12772, stack(0x000000a9aba00000,0x000000a9abb00000) (1024K)]
  0x000002385c8e5af0 JavaThread "Worker-51"                         [_thread_blocked, id=11184, stack(0x000000a9aac00000,0x000000a9aad00000) (1024K)]
  0x000002385c8e6810 JavaThread "Timer-34"                          [_thread_blocked, id=2124, stack(0x000000a9abc00000,0x000000a9abd00000) (1024K)]
  0x000002385c8e6ea0 JavaThread "Timer-35"                          [_thread_blocked, id=9852, stack(0x000000a9abd00000,0x000000a9abe00000) (1024K)]
  0x000002384aa4d090 JavaThread "Timer-36"                          [_thread_blocked, id=12116, stack(0x000000a9abe00000,0x000000a9abf00000) (1024K)]
  0x000002384aa4bce0 JavaThread "Timer-37"                          [_thread_blocked, id=4968, stack(0x000000a9ac000000,0x000000a9ac100000) (1024K)]
  0x000002384aa4c370 JavaThread "Timer-38"                          [_thread_blocked, id=17176, stack(0x000000a9ac200000,0x000000a9ac300000) (1024K)]
  0x000002384aa4fe80 JavaThread "Timer-39"                          [_thread_blocked, id=20472, stack(0x000000a9ac400000,0x000000a9ac500000) (1024K)]
  0x0000023847f5be20 JavaThread "Timer-40"                          [_thread_blocked, id=2520, stack(0x000000a9a9500000,0x000000a9a9600000) (1024K)]
  0x0000023847f5aa70 JavaThread "Timer-41"                          [_thread_blocked, id=6208, stack(0x000000a9ab600000,0x000000a9ab700000) (1024K)]
  0x0000023847f5def0 JavaThread "Timer-42"                          [_thread_blocked, id=2580, stack(0x000000a9abf00000,0x000000a9ac000000) (1024K)]
  0x0000023847f5c4b0 JavaThread "Timer-43"                          [_thread_blocked, id=6036, stack(0x000000a9ab800000,0x000000a9ab900000) (1024K)]
  0x0000023847f5d1d0 JavaThread "Timer-44"                          [_thread_blocked, id=1852, stack(0x000000a9ac300000,0x000000a9ac400000) (1024K)]
  0x0000023847f5cb40 JavaThread "Timer-45"                          [_thread_blocked, id=18724, stack(0x000000a9ac100000,0x000000a9ac200000) (1024K)]
  0x0000023847f5d860 JavaThread "Timer-46"                          [_thread_blocked, id=12716, stack(0x000000a9ac500000,0x000000a9ac600000) (1024K)]
  0x000002384aa4f7f0 JavaThread "Timer-47"                          [_thread_blocked, id=5568, stack(0x000000a9ac600000,0x000000a9ac700000) (1024K)]
  0x000002384aa4e440 JavaThread "Timer-48"                          [_thread_blocked, id=18444, stack(0x000000a9ac700000,0x000000a9ac800000) (1024K)]
  0x000002384591a220 JavaThread "Timer-49"                          [_thread_blocked, id=19624, stack(0x000000a9ac800000,0x000000a9ac900000) (1024K)]
  0x000002384591a8b0 JavaThread "Timer-50"                          [_thread_blocked, id=4236, stack(0x000000a9ac900000,0x000000a9aca00000) (1024K)]
  0x000002384591af40 JavaThread "Timer-51"                          [_thread_blocked, id=13132, stack(0x000000a9aca00000,0x000000a9acb00000) (1024K)]
  0x000002384762aba0 JavaThread "Timer-52"                          [_thread_blocked, id=19532, stack(0x000000a9acb00000,0x000000a9acc00000) (1024K)]
  0x00000238476297f0 JavaThread "Timer-53"                          [_thread_blocked, id=19984, stack(0x000000a9acc00000,0x000000a9acd00000) (1024K)]
  0x000002384762e6b0 JavaThread "Timer-54"                          [_thread_blocked, id=9708, stack(0x000000a9acd00000,0x000000a9ace00000) (1024K)]
  0x000002384762ed40 JavaThread "Timer-55"                          [_thread_blocked, id=16744, stack(0x000000a9ace00000,0x000000a9acf00000) (1024K)]
  0x000002385bf1f170 JavaThread "Timer-56"                          [_thread_blocked, id=8984, stack(0x000000a9acf00000,0x000000a9ad000000) (1024K)]
  0x000002385bf21f60 JavaThread "Timer-57"                          [_thread_blocked, id=3960, stack(0x000000a9ad000000,0x000000a9ad100000) (1024K)]
  0x000002384657a970 JavaThread "Timer-58"                          [_thread_blocked, id=16556, stack(0x000000a9ad100000,0x000000a9ad200000) (1024K)]
  0x0000023846577b80 JavaThread "Timer-59"                          [_thread_blocked, id=19992, stack(0x000000a9ad200000,0x000000a9ad300000) (1024K)]
  0x00000238465795c0 JavaThread "Timer-60"                          [_thread_blocked, id=8876, stack(0x000000a9ad300000,0x000000a9ad400000) (1024K)]
  0x000002384aa4d720 JavaThread "Timer-61"                          [_thread_blocked, id=19788, stack(0x000000a9ad800000,0x000000a9ad900000) (1024K)]
  0x000002385bf24d50 JavaThread "Timer-62"                          [_thread_blocked, id=5248, stack(0x000000a9ae300000,0x000000a9ae400000) (1024K)]
  0x000002384aa51230 JavaThread "Timer-63"                          [_thread_blocked, id=19808, stack(0x000000a9adb00000,0x000000a9adc00000) (1024K)]
  0x000002385bf225f0 JavaThread "Timer-64"                          [_thread_blocked, id=18448, stack(0x000000a9ae400000,0x000000a9ae500000) (1024K)]
  0x000002384591b5d0 JavaThread "Timer-65"                          [_thread_blocked, id=10464, stack(0x000000a9ae700000,0x000000a9ae800000) (1024K)]
  0x000002385ce3c690 JavaThread "Timer-66"                          [_thread_blocked, id=7292, stack(0x000000a9ae800000,0x000000a9ae900000) (1024K)]
  0x000002385ce3b970 JavaThread "Timer-67"                          [_thread_blocked, id=9648, stack(0x000000a9aab00000,0x000000a9aac00000) (1024K)]
  0x000002385ce3b2e0 JavaThread "Timer-68"                          [_thread_blocked, id=7432, stack(0x000000a9ae900000,0x000000a9aea00000) (1024K)]
  0x000002385ce3e0d0 JavaThread "Timer-69"                          [_thread_blocked, id=19444, stack(0x000000a9aea00000,0x000000a9aeb00000) (1024K)]
  0x000002385ce3da40 JavaThread "Timer-70"                          [_thread_blocked, id=9116, stack(0x000000a9aeb00000,0x000000a9aec00000) (1024K)]
  0x000002384aa50510 JavaThread "Timer-71"                          [_thread_blocked, id=3812, stack(0x000000a9aed00000,0x000000a9aee00000) (1024K)]
  0x000002385d06ee50 JavaThread "Timer-72"                          [_thread_blocked, id=2596, stack(0x000000a9aec00000,0x000000a9aed00000) (1024K)]
  0x000002385d06f4e0 JavaThread "Timer-73"                          [_thread_blocked, id=19472, stack(0x000000a9aee00000,0x000000a9aef00000) (1024K)]
  0x000002385d06c6f0 JavaThread "Timer-74"                          [_thread_blocked, id=2588, stack(0x000000a9aef00000,0x000000a9af000000) (1024K)]
  0x000002385d06c060 JavaThread "Timer-75"                          [_thread_blocked, id=10992, stack(0x000000a9af000000,0x000000a9af100000) (1024K)]
  0x000002385d06daa0 JavaThread "Timer-76"                          [_thread_blocked, id=8188, stack(0x000000a9af100000,0x000000a9af200000) (1024K)]
  0x000002385d06cd80 JavaThread "Timer-77"                          [_thread_blocked, id=6008, stack(0x000000a9af200000,0x000000a9af300000) (1024K)]
  0x000002385d06d410 JavaThread "Timer-78"                          [_thread_blocked, id=4620, stack(0x000000a9af300000,0x000000a9af400000) (1024K)]
  0x000002385d06e130 JavaThread "Timer-79"                          [_thread_blocked, id=18372, stack(0x000000a9af400000,0x000000a9af500000) (1024K)]
  0x000002385ce3ac50 JavaThread "Timer-80"                          [_thread_blocked, id=2632, stack(0x000000a9af600000,0x000000a9af700000) (1024K)]
  0x0000023860219e70 JavaThread "Timer-81"                          [_thread_blocked, id=15824, stack(0x000000a9af500000,0x000000a9af600000) (1024K)]
  0x0000023860217da0 JavaThread "Timer-82"                          [_thread_blocked, id=1272, stack(0x000000a9af700000,0x000000a9af800000) (1024K)]
  0x0000023860218ac0 JavaThread "Timer-83"                          [_thread_blocked, id=16468, stack(0x000000a9af800000,0x000000a9af900000) (1024K)]
  0x0000023860219150 JavaThread "Timer-84"                          [_thread_blocked, id=7192, stack(0x000000a9afb00000,0x000000a9afc00000) (1024K)]
  0x00000238602197e0 JavaThread "Timer-85"                          [_thread_blocked, id=2920, stack(0x000000a9af900000,0x000000a9afa00000) (1024K)]
  0x000002386021a500 JavaThread "Timer-86"                          [_thread_blocked, id=20132, stack(0x000000a9afa00000,0x000000a9afb00000) (1024K)]
  0x0000023860217080 JavaThread "Timer-87"                          [_thread_blocked, id=11636, stack(0x000000a9afc00000,0x000000a9afd00000) (1024K)]
  0x000002386021ab90 JavaThread "Timer-88"                          [_thread_blocked, id=5024, stack(0x000000a9afd00000,0x000000a9afe00000) (1024K)]
  0x000002386021b220 JavaThread "Timer-89"                          [_thread_blocked, id=5688, stack(0x000000a9aff00000,0x000000a9b0000000) (1024K)]
  0x0000023860217710 JavaThread "Timer-90"                          [_thread_blocked, id=14444, stack(0x000000a9afe00000,0x000000a9aff00000) (1024K)]
  0x000002386021b8b0 JavaThread "Timer-91"                          [_thread_blocked, id=12560, stack(0x000000a9b0000000,0x000000a9b0100000) (1024K)]
  0x0000023860218430 JavaThread "Timer-92"                          [_thread_blocked, id=12396, stack(0x000000a9b0100000,0x000000a9b0200000) (1024K)]
  0x000002386021cc60 JavaThread "Timer-93"                          [_thread_blocked, id=5056, stack(0x000000a9b0400000,0x000000a9b0500000) (1024K)]
  0x000002386021bf40 JavaThread "Timer-94"                          [_thread_blocked, id=9476, stack(0x000000a9ae000000,0x000000a9ae100000) (1024K)]
  0x000002386021c5d0 JavaThread "Timer-95"                          [_thread_blocked, id=1976, stack(0x000000a9b0200000,0x000000a9b0300000) (1024K)]
  0x000002386021d2f0 JavaThread "Timer-96"                          [_thread_blocked, id=8124, stack(0x000000a9b0300000,0x000000a9b0400000) (1024K)]
  0x000002386021d980 JavaThread "Timer-97"                          [_thread_blocked, id=176, stack(0x000000a9b0500000,0x000000a9b0600000) (1024K)]
  0x000002386021e6a0 JavaThread "Timer-98"                          [_thread_blocked, id=10736, stack(0x000000a9b0700000,0x000000a9b0800000) (1024K)]
  0x000002385d06e7c0 JavaThread "Timer-99"                          [_thread_blocked, id=5108, stack(0x000000a9b0800000,0x000000a9b0900000) (1024K)]
  0x000002386021e010 JavaThread "Timer-100"                         [_thread_blocked, id=14776, stack(0x000000a9b0600000,0x000000a9b0700000) (1024K)]
  0x000002385bf24030 JavaThread "Timer-101"                         [_thread_blocked, id=9172, stack(0x000000a9b0900000,0x000000a9b0a00000) (1024K)]
  0x000002384b18b6f0 JavaThread "Timer-102"                         [_thread_blocked, id=10620, stack(0x000000a9b0c00000,0x000000a9b0d00000) (1024K)]
  0x000002384b18bd80 JavaThread "Timer-103"                         [_thread_blocked, id=4184, stack(0x000000a9b0a00000,0x000000a9b0b00000) (1024K)]
  0x000002384b18d130 JavaThread "Timer-104"                         [_thread_blocked, id=16776, stack(0x000000a9b0b00000,0x000000a9b0c00000) (1024K)]
  0x000002384b18d7c0 JavaThread "Timer-105"                         [_thread_blocked, id=8296, stack(0x000000a9b0d00000,0x000000a9b0e00000) (1024K)]
  0x000002384b18de50 JavaThread "Timer-106"                         [_thread_blocked, id=8716, stack(0x000000a9b0e00000,0x000000a9b0f00000) (1024K)]
  0x000002384b18e4e0 JavaThread "Timer-107"                         [_thread_blocked, id=8900, stack(0x000000a9b1100000,0x000000a9b1200000) (1024K)]
  0x000002384b18b060 JavaThread "Timer-108"                         [_thread_blocked, id=3860, stack(0x000000a9b1300000,0x000000a9b1400000) (1024K)]
  0x000002384b18c410 JavaThread "Timer-109"                         [_thread_blocked, id=7528, stack(0x000000a9b1200000,0x000000a9b1300000) (1024K)]
  0x000002384b18caa0 JavaThread "Timer-110"                         [_thread_blocked, id=6744, stack(0x000000a9b1400000,0x000000a9b1500000) (1024K)]
  0x000002385e4ac380 JavaThread "Timer-111"                         [_thread_blocked, id=14532, stack(0x000000a9b1700000,0x000000a9b1800000) (1024K)]
  0x000002385e4aca10 JavaThread "Timer-112"                         [_thread_blocked, id=21240, stack(0x000000a9ae100000,0x000000a9ae200000) (1024K)]
  0x000002385e4addc0 JavaThread "Timer-113"                         [_thread_blocked, id=21232, stack(0x000000a9b1500000,0x000000a9b1600000) (1024K)]
  0x000002385e4ae450 JavaThread "Timer-114"                         [_thread_blocked, id=21220, stack(0x000000a9b1600000,0x000000a9b1700000) (1024K)]
  0x000002385e4ad0a0 JavaThread "Timer-115"                         [_thread_blocked, id=7124, stack(0x000000a9b1800000,0x000000a9b1900000) (1024K)]
  0x000002385e4aafd0 JavaThread "Timer-116"                         [_thread_blocked, id=17460, stack(0x000000a9b1a00000,0x000000a9b1b00000) (1024K)]
  0x000002385e4ad730 JavaThread "Timer-117"                         [_thread_blocked, id=17492, stack(0x000000a9b1900000,0x000000a9b1a00000) (1024K)]
  0x000002385e4aeae0 JavaThread "Timer-118"                         [_thread_blocked, id=7452, stack(0x000000a9b1b00000,0x000000a9b1c00000) (1024K)]
  0x000002385e4af170 JavaThread "Timer-119"                         [_thread_blocked, id=15472, stack(0x000000a9b1c00000,0x000000a9b1d00000) (1024K)]
  0x000002385e4abcf0 JavaThread "Timer-120"                         [_thread_blocked, id=7048, stack(0x000000a9b1f00000,0x000000a9b2000000) (1024K)]
  0x000002385e4afe90 JavaThread "Timer-121"                         [_thread_blocked, id=15076, stack(0x000000a9b1d00000,0x000000a9b1e00000) (1024K)]
  0x000002385e4b0520 JavaThread "Timer-122"                         [_thread_blocked, id=12428, stack(0x000000a9b1e00000,0x000000a9b1f00000) (1024K)]
  0x000002385e4af800 JavaThread "Timer-123"                         [_thread_blocked, id=19856, stack(0x000000a9b2000000,0x000000a9b2100000) (1024K)]
  0x000002385e4b4030 JavaThread "Timer-124"                         [_thread_blocked, id=20512, stack(0x000000a9b2100000,0x000000a9b2200000) (1024K)]
  0x000002385e4b0bb0 JavaThread "Timer-125"                         [_thread_blocked, id=21172, stack(0x000000a9b2300000,0x000000a9b2400000) (1024K)]
  0x000002385e4b1f60 JavaThread "Timer-126"                         [_thread_blocked, id=5680, stack(0x000000a9b2400000,0x000000a9b2500000) (1024K)]
  0x000002385e4b25f0 JavaThread "Timer-127"                         [_thread_blocked, id=21296, stack(0x000000a9b2500000,0x000000a9b2600000) (1024K)]
  0x000002385e4b46c0 JavaThread "Timer-128"                         [_thread_blocked, id=8160, stack(0x000000a9b2600000,0x000000a9b2700000) (1024K)]
  0x000002385e4b39a0 JavaThread "Timer-129"                         [_thread_blocked, id=21500, stack(0x000000a9b2a00000,0x000000a9b2b00000) (1024K)]
  0x000002385e4b18d0 JavaThread "Timer-130"                         [_thread_blocked, id=7676, stack(0x000000a9abb00000,0x000000a9abc00000) (1024K)]
  0x000002385e4b4d50 JavaThread "Timer-131"                         [_thread_blocked, id=10480, stack(0x000000a9ae200000,0x000000a9ae300000) (1024K)]
  0x000002385e4b2c80 JavaThread "Timer-132"                         [_thread_blocked, id=19988, stack(0x000000a9b0f00000,0x000000a9b1000000) (1024K)]
  0x000002385e4b53e0 JavaThread "Timer-133"                         [_thread_blocked, id=12824, stack(0x000000a9ab900000,0x000000a9aba00000) (1024K)]
  0x000002385e4b3310 JavaThread "Timer-134"                         [_thread_blocked, id=15144, stack(0x000000a9b2700000,0x000000a9b2800000) (1024K)]
  0x000002385e4b8860 JavaThread "Timer-135"                         [_thread_blocked, id=7540, stack(0x000000a9b1000000,0x000000a9b1100000) (1024K)]
  0x000002385e4b8ef0 JavaThread "Timer-136"                         [_thread_blocked, id=5932, stack(0x000000a9b2800000,0x000000a9b2900000) (1024K)]
  0x000002385e4b6100 JavaThread "Timer-137"                         [_thread_blocked, id=6900, stack(0x000000a9b2900000,0x000000a9b2a00000) (1024K)]
  0x000002385e4b5a70 JavaThread "Timer-138"                         [_thread_blocked, id=17340, stack(0x000000a9b2d00000,0x000000a9b2e00000) (1024K)]
  0x000002385e4b7b40 JavaThread "Worker-54"                         [_thread_blocked, id=21112, stack(0x000000a9b3000000,0x000000a9b3100000) (1024K)]
  0x000002385e4b9c10 JavaThread "Timer-139"                         [_thread_blocked, id=7136, stack(0x000000a9b2b00000,0x000000a9b2c00000) (1024K)]
  0x000002385e4b81d0 JavaThread "Timer-140"                         [_thread_blocked, id=5804, stack(0x000000a9b2c00000,0x000000a9b2d00000) (1024K)]
  0x000002385e4b74b0 JavaThread "Timer-141"                         [_thread_blocked, id=20964, stack(0x000000a9b2e00000,0x000000a9b2f00000) (1024K)]
  0x000002385e4b9580 JavaThread "Timer-142"                         [_thread_blocked, id=3276, stack(0x000000a9b2f00000,0x000000a9b3000000) (1024K)]
  0x000002385e4ba2a0 JavaThread "Timer-143"                         [_thread_blocked, id=1248, stack(0x000000a9b3200000,0x000000a9b3300000) (1024K)]
  0x000002385e4b6790 JavaThread "Timer-144"                         [_thread_blocked, id=4092, stack(0x000000a9b3100000,0x000000a9b3200000) (1024K)]
  0x000002385e4b6e20 JavaThread "Timer-145"                         [_thread_blocked, id=20024, stack(0x000000a9b3300000,0x000000a9b3400000) (1024K)]
  0x000002385e4ba930 JavaThread "Timer-146"                         [_thread_blocked, id=11624, stack(0x000000a9b3400000,0x000000a9b3500000) (1024K)]
  0x000002385bf253e0 JavaThread "Timer-147"                         [_thread_blocked, id=6400, stack(0x000000a9b3600000,0x000000a9b3700000) (1024K)]
  0x000002385c8e6180 JavaThread "Timer-148"                         [_thread_blocked, id=13368, stack(0x000000a9b3500000,0x000000a9b3600000) (1024K)]
  0x000002385bf246c0 JavaThread "Timer-149"                         [_thread_blocked, id=20312, stack(0x000000a9b3700000,0x000000a9b3800000) (1024K)]
  0x000002384aa53300 JavaThread "Timer-150"                         [_thread_blocked, id=19440, stack(0x000000a9b3800000,0x000000a9b3900000) (1024K)]
  0x000002385ea785a0 JavaThread "Timer-151"                         [_thread_blocked, id=13148, stack(0x000000a9b3900000,0x000000a9b3a00000) (1024K)]
  0x000002385ea76b60 JavaThread "Timer-152"                         [_thread_blocked, id=8564, stack(0x000000a9b3b00000,0x000000a9b3c00000) (1024K)]
  0x000002385ea77f10 JavaThread "Timer-153"                         [_thread_blocked, id=17320, stack(0x000000a9b3a00000,0x000000a9b3b00000) (1024K)]
  0x000002385ea757b0 JavaThread "Timer-154"                         [_thread_blocked, id=21328, stack(0x000000a9b3c00000,0x000000a9b3d00000) (1024K)]
  0x000002385ea77880 JavaThread "Timer-155"                         [_thread_blocked, id=5028, stack(0x000000a9b3d00000,0x000000a9b3e00000) (1024K)]
  0x000002385ea75e40 JavaThread "Timer-156"                         [_thread_blocked, id=20568, stack(0x000000a9b4000000,0x000000a9b4100000) (1024K)]
  0x000002385ea75120 JavaThread "Timer-157"                         [_thread_blocked, id=8868, stack(0x000000a9b3e00000,0x000000a9b3f00000) (1024K)]
  0x000002385ea792c0 JavaThread "Timer-158"                         [_thread_blocked, id=18564, stack(0x000000a9b3f00000,0x000000a9b4000000) (1024K)]
  0x000002385ea78c30 JavaThread "Timer-159"                         [_thread_blocked, id=11576, stack(0x000000a9b4100000,0x000000a9b4200000) (1024K)]
  0x000002385ea764d0 JavaThread "Timer-160"                         [_thread_blocked, id=21308, stack(0x000000a9b4200000,0x000000a9b4300000) (1024K)]
  0x000002385ea7a670 JavaThread "Timer-161"                         [_thread_blocked, id=6740, stack(0x000000a9b4400000,0x000000a9b4500000) (1024K)]
  0x000002385ea7c0b0 JavaThread "Timer-162"                         [_thread_blocked, id=20820, stack(0x000000a9b4300000,0x000000a9b4400000) (1024K)]
  0x000002385ea771f0 JavaThread "Timer-163"                         [_thread_blocked, id=7288, stack(0x000000a9b4500000,0x000000a9b4600000) (1024K)]
  0x000002385ea7ba20 JavaThread "Timer-164"                         [_thread_blocked, id=21288, stack(0x000000a9b4600000,0x000000a9b4700000) (1024K)]
  0x000002385ea7cdd0 JavaThread "Timer-165"                         [_thread_blocked, id=16684, stack(0x000000a9b4700000,0x000000a9b4800000) (1024K)]
  0x000002385ea7d460 JavaThread "Timer-166"                         [_thread_blocked, id=12920, stack(0x000000a9b4800000,0x000000a9b4900000) (1024K)]
  0x000002385ea7daf0 JavaThread "Timer-167"                         [_thread_blocked, id=16872, stack(0x000000a9b4900000,0x000000a9b4a00000) (1024K)]
  0x000002385ea7ad00 JavaThread "Timer-168"                         [_thread_blocked, id=17276, stack(0x000000a9b4a00000,0x000000a9b4b00000) (1024K)]
  0x000002385ea79fe0 JavaThread "Timer-169"                         [_thread_blocked, id=12536, stack(0x000000a9b4c00000,0x000000a9b4d00000) (1024K)]
  0x000002385ea7b390 JavaThread "Timer-170"                         [_thread_blocked, id=18216, stack(0x000000a9b4d00000,0x000000a9b4e00000) (1024K)]
  0x000002385ea7f530 JavaThread "Timer-171"                         [_thread_blocked, id=20532, stack(0x000000a9b5200000,0x000000a9b5300000) (1024K)]
  0x00000238617a3dd0 JavaThread "Timer-172"                         [_thread_blocked, id=8832, stack(0x000000a9b6200000,0x000000a9b6300000) (1024K)]
  0x00000238617a5810 JavaThread "Timer-173"                         [_thread_blocked, id=21184, stack(0x000000a9b6400000,0x000000a9b6500000) (1024K)]
  0x00000238617a78e0 JavaThread "Timer-174"                         [_thread_blocked, id=15216, stack(0x000000a9b5300000,0x000000a9b5400000) (1024K)]
  0x00000238617a7250 JavaThread "Timer-175"                         [_thread_blocked, id=19688, stack(0x000000a9b5000000,0x000000a9b5100000) (1024K)]
  0x00000238617a7f70 JavaThread "Timer-176"                         [_thread_blocked, id=6716, stack(0x000000a9b6300000,0x000000a9b6400000) (1024K)]
  0x00000238617a4af0 JavaThread "Timer-177"                         [_thread_blocked, id=6076, stack(0x000000a9b6600000,0x000000a9b6700000) (1024K)]
Total: 212

Other Threads:
  0x000002387a1125f0 VMThread "VM Thread"                           [id=4960, stack(0x000000a9a7700000,0x000000a9a7800000) (1024K)]
  0x000002387a04ca00 WatcherThread "VM Periodic Task Thread"        [id=17096, stack(0x000000a9a7600000,0x000000a9a7700000) (1024K)]
  0x0000023865d99e70 WorkerThread "GC Thread#0"                     [id=16668, stack(0x000000a9a7500000,0x000000a9a7600000) (1024K)]
  0x00000238457e0510 WorkerThread "GC Thread#1"                     [id=17800, stack(0x000000a9a8300000,0x000000a9a8400000) (1024K)]
  0x00000238457e08b0 WorkerThread "GC Thread#2"                     [id=17808, stack(0x000000a9a8400000,0x000000a9a8500000) (1024K)]
  0x0000023845370300 WorkerThread "GC Thread#3"                     [id=17812, stack(0x000000a9a8500000,0x000000a9a8600000) (1024K)]
  0x00000238453706a0 WorkerThread "GC Thread#4"                     [id=17816, stack(0x000000a9a8600000,0x000000a9a8700000) (1024K)]
  0x0000023845370a40 WorkerThread "GC Thread#5"                     [id=17820, stack(0x000000a9a8700000,0x000000a9a8800000) (1024K)]
  0x0000023845370de0 WorkerThread "GC Thread#6"                     [id=17824, stack(0x000000a9a8800000,0x000000a9a8900000) (1024K)]
  0x0000023845c6f540 WorkerThread "GC Thread#7"                     [id=17956, stack(0x000000a9a8900000,0x000000a9a8a00000) (1024K)]
Total: 10

Threads with active compile tasks:
C1 CompilerThread0  12075797 18735   !   3       org.eclipse.jdt.ls.core.internal.JDTUtils::getFakeCompilationUnit (329 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000023800000000-0x0000023800ba0000-0x0000023800ba0000), size 12189696, SharedBaseAddress: 0x0000023800000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023801000000-0x0000023841000000, reserved size: 1073741824
Narrow klass base: 0x0000023800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32612M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 6656K, used 366K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 3% used [0x00000000eab00000,0x00000000eab3b970,0x00000000eb100000)
  from space 512K, 25% used [0x00000000eb880000,0x00000000eb8a0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505137K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded4c6d8,0x00000000ded80000)
 Metaspace       used 86556K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K

Card table byte_map: [0x0000023865730000,0x0000023865940000] _byte_map_base: 0x0000023865130000

Marking Bits: (ParMarkBitMap*) 0x00007fff775931f0
 Begin Bits: [0x0000023877f70000, 0x0000023878f70000)
 End Bits:   [0x0000023878f70000, 0x0000023879f70000)

Polling page: 0x0000023865510000

Metaspace:

Usage:
  Non-class:     74.46 MB used.
      Class:     10.07 MB used.
       Both:     84.53 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      75.75 MB ( 59%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      11.06 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      86.81 MB (  8%) committed. 

Chunk freelists:
   Non-Class:  4.66 MB
       Class:  5.02 MB
        Both:  9.68 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 144.62 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 2162.
num_arena_deaths: 592.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1389.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 648.
num_chunks_taken_from_freelist: 6428.
num_chunk_merges: 190.
num_chunk_splits: 3989.
num_chunks_enlarged: 2301.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=13796Kb max_used=17893Kb free=106203Kb
 bounds [0x0000023870a40000, 0x0000023871bc0000, 0x0000023877f70000]
CodeHeap 'profiled nmethods': size=120000Kb used=22676Kb max_used=31338Kb free=97323Kb
 bounds [0x0000023868f70000, 0x000002386ae10000, 0x00000238704a0000]
CodeHeap 'non-nmethods': size=5760Kb used=1423Kb max_used=1569Kb free=4336Kb
 bounds [0x00000238704a0000, 0x0000023870710000, 0x0000023870a40000]
 total_blobs=12020 nmethods=11283 adapters=642
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 10870.873 Thread 0x0000023845051770 nmethod 18725 0x000002386918a990 code [0x000002386918acc0, 0x000002386918c228]
Event: 11050.364 Thread 0x0000023845051770 18726       3       java.util.stream.DistinctOps$1$2::begin (24 bytes)
Event: 11050.365 Thread 0x0000023845051770 nmethod 18726 0x000002386909fb90 code [0x000002386909fd80, 0x00000238690a02f8]
Event: 11223.738 Thread 0x0000023845051770 18727       3       org.eclipse.lsp4j.jsonrpc.json.adapters.CollectionTypeAdapter$Factory$$Lambda/0x000002380145cc88::get (8 bytes)
Event: 11223.739 Thread 0x0000023845051770 nmethod 18727 0x000002386959b210 code [0x000002386959b3e0, 0x000002386959b6e0]
Event: 11359.587 Thread 0x000002387a136410 18728   !   4       java.util.concurrent.ConcurrentHashMap::putVal (432 bytes)
Event: 11359.606 Thread 0x000002387a136410 nmethod 18728 0x00000238715eb210 code [0x00000238715eb480, 0x00000238715ec2c0]
Event: 11459.569 Thread 0x000002387a136410 18729       4       org.eclipse.jdt.ls.core.internal.filesystem.JLSFileSystem::getStore (76 bytes)
Event: 11459.583 Thread 0x000002387a136410 nmethod 18729 0x0000023870acb010 code [0x0000023870acb280, 0x0000023870acbc20]
Event: 11650.366 Thread 0x0000023845051770 18730       3       org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint::getArguments (173 bytes)
Event: 11650.368 Thread 0x0000023845051770 nmethod 18730 0x0000023868fbdf90 code [0x0000023868fbe340, 0x0000023868fbfeb0]
Event: 12020.358 Thread 0x000002387a136410 18731       4       java.nio.ByteBuffer::allocate (20 bytes)
Event: 12020.362 Thread 0x000002387a136410 nmethod 18731 0x0000023870a59c10 code [0x0000023870a59da0, 0x0000023870a5a028]
Event: 12058.785 Thread 0x000002387a136410 18732       4       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory$FieldReflectionAdapter::finalize (2 bytes)
Event: 12058.785 Thread 0x0000023845051770 18733   !   3       org.eclipse.lsp4j.jsonrpc.RemoteEndpoint::handleNotification (46 bytes)
Event: 12058.786 Thread 0x000002387a136410 nmethod 18732 0x0000023870aa5610 code [0x0000023870aa57a0, 0x0000023870aa5828]
Event: 12058.786 Thread 0x0000023845051770 nmethod 18733 0x0000023869165210 code [0x0000023869165420, 0x0000023869165910]
Event: 12058.786 Thread 0x0000023845051770 18734       3       org.eclipse.lsp4j.jsonrpc.util.LimitedInputStream::close (20 bytes)
Event: 12058.786 Thread 0x0000023845051770 nmethod 18734 0x00000238690c5790 code [0x00000238690c5940, 0x00000238690c5b58]
Event: 12075.703 Thread 0x0000023845051770 18735   !   3       org.eclipse.jdt.ls.core.internal.JDTUtils::getFakeCompilationUnit (329 bytes)

GC Heap History (20 events):
Event: 2076.148 GC heap before
{Heap before GC invocations=20084 (full 8):
 PSYoungGen      total 9728K, used 6336K [0x00000000eab00000, 0x00000000eba00000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 3584K, 5% used [0x00000000eb380000,0x00000000eb3b0000,0x00000000eb700000)
  to   space 1024K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eba00000)
 ParOldGen       total 504832K, used 504769K [0x00000000c0000000, 0x00000000ded00000, 0x00000000eab00000)
  object space 504832K, 99% used [0x00000000c0000000,0x00000000decf06d8,0x00000000ded00000)
 Metaspace       used 86420K, committed 88896K, reserved 1179648K
  class space    used 10310K, committed 11328K, reserved 1048576K
}
Event: 2076.150 GC heap after
{Heap after GC invocations=20084 (full 8):
 PSYoungGen      total 6656K, used 160K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb900000,0x00000000eb928000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 504832K, used 504825K [0x00000000c0000000, 0x00000000ded00000, 0x00000000eab00000)
  object space 504832K, 99% used [0x00000000c0000000,0x00000000decfe6d8,0x00000000ded00000)
 Metaspace       used 86420K, committed 88896K, reserved 1179648K
  class space    used 10310K, committed 11328K, reserved 1048576K
}
Event: 3045.897 GC heap before
{Heap before GC invocations=20085 (full 8):
 PSYoungGen      total 6656K, used 6304K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb900000,0x00000000eb928000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 504832K, used 504825K [0x00000000c0000000, 0x00000000ded00000, 0x00000000eab00000)
  object space 504832K, 99% used [0x00000000c0000000,0x00000000decfe6d8,0x00000000ded00000)
 Metaspace       used 86440K, committed 88896K, reserved 1179648K
  class space    used 10310K, committed 11328K, reserved 1048576K
}
Event: 3045.900 GC heap after
{Heap after GC invocations=20085 (full 8):
 PSYoungGen      total 6656K, used 160K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb880000,0x00000000eb8a8000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 504849K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded046d8,0x00000000ded80000)
 Metaspace       used 86440K, committed 88896K, reserved 1179648K
  class space    used 10310K, committed 11328K, reserved 1048576K
}
Event: 7417.653 GC heap before
{Heap before GC invocations=20086 (full 8):
 PSYoungGen      total 6656K, used 6304K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb880000,0x00000000eb8a8000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 504849K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded046d8,0x00000000ded80000)
 Metaspace       used 86454K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 7417.656 GC heap after
{Heap after GC invocations=20086 (full 8):
 PSYoungGen      total 6656K, used 160K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb900000,0x00000000eb928000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 504881K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded0c6d8,0x00000000ded80000)
 Metaspace       used 86454K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 8230.440 GC heap before
{Heap before GC invocations=20087 (full 8):
 PSYoungGen      total 6656K, used 6304K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb900000,0x00000000eb928000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 504881K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded0c6d8,0x00000000ded80000)
 Metaspace       used 86478K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 8230.443 GC heap after
{Heap after GC invocations=20087 (full 8):
 PSYoungGen      total 6656K, used 192K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb880000,0x00000000eb8b0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 504929K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded186d8,0x00000000ded80000)
 Metaspace       used 86478K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 8642.620 GC heap before
{Heap before GC invocations=20088 (full 8):
 PSYoungGen      total 6656K, used 6336K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb880000,0x00000000eb8b0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 504929K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded186d8,0x00000000ded80000)
 Metaspace       used 86483K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 8642.621 GC heap after
{Heap after GC invocations=20088 (full 8):
 PSYoungGen      total 6656K, used 192K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb900000,0x00000000eb930000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 504953K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded1e6d8,0x00000000ded80000)
 Metaspace       used 86483K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 9349.452 GC heap before
{Heap before GC invocations=20089 (full 8):
 PSYoungGen      total 6656K, used 6336K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb900000,0x00000000eb930000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 504953K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded1e6d8,0x00000000ded80000)
 Metaspace       used 86503K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 9349.456 GC heap after
{Heap after GC invocations=20089 (full 8):
 PSYoungGen      total 6656K, used 160K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb880000,0x00000000eb8a8000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505001K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded2a6d8,0x00000000ded80000)
 Metaspace       used 86503K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 10186.807 GC heap before
{Heap before GC invocations=20090 (full 8):
 PSYoungGen      total 6656K, used 6304K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 31% used [0x00000000eb880000,0x00000000eb8a8000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505001K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded2a6d8,0x00000000ded80000)
 Metaspace       used 86549K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 10186.809 GC heap after
{Heap after GC invocations=20090 (full 8):
 PSYoungGen      total 6656K, used 256K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 50% used [0x00000000eb900000,0x00000000eb940000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 505033K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded326d8,0x00000000ded80000)
 Metaspace       used 86549K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 10857.879 GC heap before
{Heap before GC invocations=20091 (full 8):
 PSYoungGen      total 6656K, used 6400K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 50% used [0x00000000eb900000,0x00000000eb940000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 505033K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded326d8,0x00000000ded80000)
 Metaspace       used 86553K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 10857.881 GC heap after
{Heap after GC invocations=20091 (full 8):
 PSYoungGen      total 6656K, used 192K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb880000,0x00000000eb8b0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505097K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded426d8,0x00000000ded80000)
 Metaspace       used 86553K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 11359.589 GC heap before
{Heap before GC invocations=20092 (full 8):
 PSYoungGen      total 6656K, used 6336K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 37% used [0x00000000eb880000,0x00000000eb8b0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505097K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded426d8,0x00000000ded80000)
 Metaspace       used 86554K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 11359.592 GC heap after
{Heap after GC invocations=20092 (full 8):
 PSYoungGen      total 6656K, used 224K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 43% used [0x00000000eb900000,0x00000000eb938000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 505137K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded4c6d8,0x00000000ded80000)
 Metaspace       used 86554K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 12075.696 GC heap before
{Heap before GC invocations=20093 (full 8):
 PSYoungGen      total 6656K, used 6368K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000eab00000,0x00000000eb100000,0x00000000eb100000)
  from space 512K, 43% used [0x00000000eb900000,0x00000000eb938000,0x00000000eb980000)
  to   space 512K, 0% used [0x00000000eb880000,0x00000000eb880000,0x00000000eb900000)
 ParOldGen       total 505344K, used 505137K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded4c6d8,0x00000000ded80000)
 Metaspace       used 86555K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}
Event: 12075.700 GC heap after
{Heap after GC invocations=20093 (full 8):
 PSYoungGen      total 6656K, used 128K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb100000)
  from space 512K, 25% used [0x00000000eb880000,0x00000000eb8a0000,0x00000000eb900000)
  to   space 512K, 0% used [0x00000000eb900000,0x00000000eb900000,0x00000000eb980000)
 ParOldGen       total 505344K, used 505137K [0x00000000c0000000, 0x00000000ded80000, 0x00000000eab00000)
  object space 505344K, 99% used [0x00000000c0000000,0x00000000ded4c6d8,0x00000000ded80000)
 Metaspace       used 86555K, committed 88896K, reserved 1179648K
  class space    used 10312K, committed 11328K, reserved 1048576K
}

Dll operation events (16 events):
Event: 0.015 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.258 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.308 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.343 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.347 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.352 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.422 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.681 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 11.528 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 32.764 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-70097797\jna1165009037567056538.dll
Event: 113.312 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 138.751 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 138.757 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 151.398 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
Event: 155.295 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
Event: 262.137 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll

Deoptimization events (20 events):
Event: 5419.117 Thread 0x000002385e4addc0 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9b15fef30
Event: 5419.117 Thread 0x000002385e4addc0 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9b15fe408 mode 0
Event: 5419.414 Thread 0x000002385c8e4dd0 DEOPT PACKING pc=0x000002386aa522a3 sp=0x000000a9ad6fef30
Event: 5419.414 Thread 0x000002385c8e4dd0 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9ad6fe418 mode 0
Event: 5579.183 Thread 0x000002385e4aeae0 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9b1bff480
Event: 5579.183 Thread 0x000002385e4aeae0 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9b1bfe958 mode 0
Event: 5867.097 Thread 0x000002386021bf40 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9ae0ff5b0
Event: 5867.097 Thread 0x000002386021bf40 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9ae0fea88 mode 0
Event: 6165.959 Thread 0x000002384aa4c370 DEOPT PACKING pc=0x000002386aa522a3 sp=0x000000a9ac2ff600
Event: 6165.959 Thread 0x000002384aa4c370 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9ac2feae8 mode 0
Event: 7050.928 Thread 0x0000023847f5cb40 DEOPT PACKING pc=0x000002386aa522a3 sp=0x000000a9ac1ff5c0
Event: 7050.928 Thread 0x0000023847f5cb40 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9ac1feaa8 mode 0
Event: 7473.205 Thread 0x000002385e4afe90 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9b1dff090
Event: 7473.205 Thread 0x000002385e4afe90 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9b1dfe568 mode 0
Event: 8135.095 Thread 0x000002385e4ae450 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9b16ff060
Event: 8135.095 Thread 0x000002385e4ae450 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9b16fe538 mode 0
Event: 9031.092 Thread 0x0000023860217710 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9afeff540
Event: 9031.092 Thread 0x0000023860217710 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9afefea18 mode 0
Event: 10951.046 Thread 0x000002385d06e7c0 DEOPT PACKING pc=0x000002386aa53c4a sp=0x000000a9b08ff0d0
Event: 10951.046 Thread 0x000002385d06e7c0 DEOPT UNPACKING pc=0x00000238704f4242 sp=0x000000a9b08fe5a8 mode 0

Classes loaded (20 events):
Event: 425.333 Loading class java/util/function/LongFunction
Event: 425.334 Loading class java/util/function/LongFunction done
Event: 425.334 Loading class java/util/stream/Nodes$ConcNode
Event: 425.334 Loading class java/util/stream/Nodes$AbstractConcNode
Event: 425.335 Loading class java/util/stream/Nodes$AbstractConcNode done
Event: 425.335 Loading class java/util/stream/Nodes$ConcNode done
Event: 1056.431 Loading class java/util/concurrent/CompletableFuture$UniApply
Event: 1056.431 Loading class java/util/concurrent/CompletableFuture$UniCompletion
Event: 1056.431 Loading class java/util/concurrent/CompletableFuture$UniCompletion done
Event: 1056.431 Loading class java/util/concurrent/CompletableFuture$UniApply done
Event: 1056.467 Loading class java/util/stream/DistinctOps
Event: 1056.467 Loading class java/util/stream/DistinctOps done
Event: 1056.467 Loading class java/util/stream/DistinctOps$1
Event: 1056.467 Loading class java/util/stream/DistinctOps$1 done
Event: 1056.473 Loading class java/util/stream/DistinctOps$1$2
Event: 1056.473 Loading class java/util/stream/DistinctOps$1$2 done
Event: 1056.539 Loading class java/util/concurrent/CompletableFuture$UniAccept
Event: 1056.539 Loading class java/util/concurrent/CompletableFuture$UniAccept done
Event: 1056.539 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 1056.540 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done

Classes unloaded (20 events):
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801016400 'java/lang/invoke/LambdaForm$MH+0x0000023801016400'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100d800 'java/lang/invoke/LambdaForm$MH+0x000002380100d800'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100d400 'java/lang/invoke/LambdaForm$MH+0x000002380100d400'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100d000 'java/lang/invoke/LambdaForm$MH+0x000002380100d000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100cc00 'java/lang/invoke/LambdaForm$MH+0x000002380100cc00'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100c800 'java/lang/invoke/LambdaForm$BMH+0x000002380100c800'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x000002380100c000 'java/lang/invoke/LambdaForm$DMH+0x000002380100c000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801008400 'java/lang/invoke/LambdaForm$MH+0x0000023801008400'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801008000 'java/lang/invoke/LambdaForm$MH+0x0000023801008000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801007c00 'java/lang/invoke/LambdaForm$MH+0x0000023801007c00'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801006800 'java/lang/invoke/LambdaForm$MH+0x0000023801006800'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801006000 'java/lang/invoke/LambdaForm$MH+0x0000023801006000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801005c00 'java/lang/invoke/LambdaForm$MH+0x0000023801005c00'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801005800 'java/lang/invoke/LambdaForm$MH+0x0000023801005800'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801005400 'java/lang/invoke/LambdaForm$BMH+0x0000023801005400'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801004000 'java/lang/invoke/LambdaForm$DMH+0x0000023801004000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801002800 'java/lang/invoke/LambdaForm$MH+0x0000023801002800'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801002400 'java/lang/invoke/LambdaForm$MH+0x0000023801002400'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801002000 'java/lang/invoke/LambdaForm$MH+0x0000023801002000'
Event: 1121.881 Thread 0x000002387a1125f0 Unloading class 0x0000023801001800 'java/lang/invoke/LambdaForm$MH+0x0000023801001800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1074.514 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb02ccb0}> (0x00000000eb02ccb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.514 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb02d130}> (0x00000000eb02d130) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.515 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb02e6c8}> (0x00000000eb02e6c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.515 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb02eb48}> (0x00000000eb02eb48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.523 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb353c68}> (0x00000000eb353c68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.523 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb3540e8}> (0x00000000eb3540e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.528 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab00780}> (0x00000000eab00780) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.528 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab00c00}> (0x00000000eab00c00) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.529 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab02198}> (0x00000000eab02198) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.529 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab02618}> (0x00000000eab02618) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.540 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae54b48}> (0x00000000eae54b48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.540 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae54fc8}> (0x00000000eae54fc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.541 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae55c28}> (0x00000000eae55c28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.541 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae560a8}> (0x00000000eae560a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.542 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae57640}> (0x00000000eae57640) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1074.542 Thread 0x0000023845919500 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae57ac0}> (0x00000000eae57ac0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1120.788 Thread 0x000002384591bc60 Implicit null exception at 0x0000023870fec4a2 to 0x0000023870fef4d8
Event: 1126.919 Thread 0x000002384591bc60 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab0d160}> (0x00000000eab0d160) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1127.191 Thread 0x000002385ea7c740 Implicit null exception at 0x0000023870c19de3 to 0x0000023870c1ad94
Event: 9649.290 Thread 0x000002384657ddf0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eab76dd8}: Found class java.lang.Object, but interface was expected> (0x00000000eab76dd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 11359.589 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11359.592 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11379.673 Executing VM operation: Cleanup
Event: 11379.673 Executing VM operation: Cleanup done
Event: 11528.943 Executing VM operation: Cleanup
Event: 11528.943 Executing VM operation: Cleanup done
Event: 11631.265 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 11631.265 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 11631.265 Executing VM operation: RendezvousGCThreads
Event: 11631.265 Executing VM operation: RendezvousGCThreads done
Event: 11811.514 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 11811.514 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 11811.514 Executing VM operation: RendezvousGCThreads
Event: 11811.514 Executing VM operation: RendezvousGCThreads done
Event: 12052.090 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 12052.090 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 12052.090 Executing VM operation: RendezvousGCThreads
Event: 12052.090 Executing VM operation: RendezvousGCThreads done
Event: 12075.696 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 12075.700 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a598510
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a59c790
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a59dd90
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a5b4c10
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a5dad90
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6b2390
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6b4f10
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6b9c10
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6d1410
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6d4810
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6f9690
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6f9e90
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a6fa510
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a7e0590
Event: 1121.916 Thread 0x000002387a1125f0 flushing osr nmethod 0x000002386a7e2310
Event: 1121.916 Thread 0x000002387a1125f0 flushing osr nmethod 0x000002386a7e4710
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a81bc90
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a83a990
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a83b190
Event: 1121.916 Thread 0x000002387a1125f0 flushing  nmethod 0x000002386a83b810

Events (20 events):
Event: 1177.145 Thread 0x000002385ea85110 Thread exited: 0x000002385ea85110
Event: 1181.657 Thread 0x0000023845919500 Thread exited: 0x0000023845919500
Event: 1181.663 Thread 0x000002385e4b1240 Thread exited: 0x000002385e4b1240
Event: 1186.922 Thread 0x000002385c8e1950 Thread exited: 0x000002385c8e1950
Event: 1186.925 Thread 0x000002385c8e4740 Thread exited: 0x000002385c8e4740
Event: 1187.830 Thread 0x000002385ea7e180 Thread exited: 0x000002385ea7e180
Event: 1237.145 Thread 0x000002385ea829b0 Thread exited: 0x000002385ea829b0
Event: 1247.831 Thread 0x000002385ea82320 Thread exited: 0x000002385ea82320
Event: 1297.146 Thread 0x000002385ea843f0 Thread exited: 0x000002385ea843f0
Event: 1307.831 Thread 0x000002385ea81600 Thread exited: 0x000002385ea81600
Event: 1351.420 Thread 0x000002385c8e5af0 Thread added: 0x00000238617a6530
Event: 1351.420 Thread 0x00000238617a6530 Thread exited: 0x00000238617a6530
Event: 1357.147 Thread 0x000002385ea7fbc0 Thread exited: 0x000002385ea7fbc0
Event: 1367.832 Thread 0x000002385ea7e810 Thread exited: 0x000002385ea7e810
Event: 1417.148 Thread 0x000002385ea857a0 Thread exited: 0x000002385ea857a0
Event: 1427.833 Thread 0x000002385ea7c740 Thread exited: 0x000002385ea7c740
Event: 1477.149 Thread 0x000002385ea84a80 Thread exited: 0x000002385ea84a80
Event: 1487.833 Thread 0x000002385ea83040 Thread exited: 0x000002385ea83040
Event: 1537.150 Thread 0x000002385ea808e0 Thread exited: 0x000002385ea808e0
Event: 1547.833 Thread 0x000002385ea7eea0 Thread exited: 0x000002385ea7eea0


Dynamic libraries:
0x00007ff76a970000 - 0x00007ff76a97e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8242c0000 - 0x00007ff824525000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff822dd0000 - 0x00007ff822e99000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff821a10000 - 0x00007ff821df8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff821830000 - 0x00007ff82197b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8078c0000 - 0x00007ff8078d8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff822560000 - 0x00007ff82272a000 	C:\WINDOWS\System32\USER32.dll
0x00007ff821e00000 - 0x00007ff821e27000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8231b0000 - 0x00007ff8231db000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff821650000 - 0x00007ff821787000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff821e30000 - 0x00007ff821ed3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff806be0000 - 0x00007ff806bfe000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff80e070000 - 0x00007ff80e30a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ff822f90000 - 0x00007ff823039000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff822190000 - 0x00007ff8221c0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff81ade0000 - 0x00007ff81adec000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007fff8cce0000 - 0x00007fff8cd6d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fff768e0000 - 0x00007fff77670000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8231e0000 - 0x00007ff823293000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff822060000 - 0x00007ff822106000 	C:\WINDOWS\System32\sechost.dll
0x00007ff823a30000 - 0x00007ff823b45000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff822110000 - 0x00007ff822184000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff81fff0000 - 0x00007ff82004e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8181d0000 - 0x00007ff8181db000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff80e310000 - 0x00007ff80e345000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff81ffd0000 - 0x00007ff81ffe4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8202c0000 - 0x00007ff8202db000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff806bd0000 - 0x00007ff806bda000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff80e3f0000 - 0x00007ff80e631000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8221d0000 - 0x00007ff822555000 	C:\WINDOWS\System32\combase.dll
0x00007ff822ea0000 - 0x00007ff822f81000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff807510000 - 0x00007ff807549000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff821790000 - 0x00007ff821829000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff806b10000 - 0x00007ff806b1f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007fffe8c60000 - 0x00007fffe8c7f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff8232b0000 - 0x00007ff8239f2000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8214d0000 - 0x00007ff821644000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff81f060000 - 0x00007ff81f8b7000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff823bd0000 - 0x00007ff823cc1000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff822d60000 - 0x00007ff822dca000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff821320000 - 0x00007ff82134f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff806bb0000 - 0x00007ff806bc8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff806ba0000 - 0x00007ff806bb0000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff819ae0000 - 0x00007ff819bfe000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff820860000 - 0x00007ff8208ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff806b50000 - 0x00007ff806b66000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff806b90000 - 0x00007ff806ba0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007fff63160000 - 0x00007fff631a5000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff8227b0000 - 0x00007ff82294e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff820b10000 - 0x00007ff820b2b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff820220000 - 0x00007ff82025a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff820900000 - 0x00007ff82092b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8212f0000 - 0x00007ff821316000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff820b30000 - 0x00007ff820b3c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff81fc20000 - 0x00007ff81fc53000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8241c0000 - 0x00007ff8241ca000 	C:\WINDOWS\System32\NSI.dll
0x00007fff61730000 - 0x00007fff61779000 	C:\Users\<USER>\AppData\Local\Temp\jna-70097797\jna1165009037567056538.dll
0x00007ff822960000 - 0x00007ff822968000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff819ab0000 - 0x00007ff819acf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff819a50000 - 0x00007ff819a75000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffee350000 - 0x00007fffee377000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ff80fe90000 - 0x00007ff80fe9a000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ff80fe80000 - 0x00007ff80fe8b000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ff80fe50000 - 0x00007ff80fe59000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
0x00007ff80fe70000 - 0x00007ff80fe7e000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ff821ee0000 - 0x00007ff822057000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff820d30000 - 0x00007ff820d60000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff820ce0000 - 0x00007ff820d1f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff80fe60000 - 0x00007ff80fe68000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff81af20000 - 0x00007ff81af47000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-70097797;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\255d4d824f24b3c8b7fd3c31bff27366\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\255d4d824f24b3c8b7fd3c31bff27366\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-5735a92c340cc2db1d98159711ca9564-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\255d4d824f24b3c8b7fd3c31bff27366\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-21\
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\NetSarang\Xshell 8\;C:\ProgramData\chocolatey\bin;C:\Program Files\Docker\Docker\resources\bin;C:\platform-tools\;C:\Program Files\nodejs\;C:\Program Files\Java\jdk-21\\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\platform-tools\;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=HuyLe
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 0 days 3:23 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 158 stepping 10 microcode 0xde, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 2401, Current Mhz: 2400, Mhz Limit: 2376

Memory: 4k page, system-wide physical 32612M (19209M free)
TotalPageFile size 34660M (AvailPageFile size 17744M)
current process WorkingSet (physical memory assigned to process): 389M, peak: 1136M
current process commit charge ("private bytes"): 898M, peak: 1084M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
