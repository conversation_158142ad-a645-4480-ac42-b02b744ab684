<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>632449728851-9ioiq6do86i0nbp6o5l75820lsm4bk5c.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.632449728851-9ioiq6do86i0nbp6o5l75820lsm4bk5c</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>632449728851-5gnhibplcih75ek7re9uvupdepk8feeh.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDwJx-sfPQGWI6hFrO_m5nNWCWUDP-6ELE</string>
	<key>GCM_SENDER_ID</key>
	<string>632449728851</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.wini.mb.chainivo</string>
	<key>PROJECT_ID</key>
	<string>rncore-noti</string>
	<key>STORAGE_BUCKET</key>
	<string>rncore-noti.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:632449728851:ios:dabaf0ad7887e00a3fa1a6</string>
</dict>
</plist>