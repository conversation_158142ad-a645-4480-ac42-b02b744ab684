# Chức năng Đổi Quà (Gift Exchange)

## Tổng quan
Chức năng đổi quà cho phép người dùng sử dụng điểm tích lũy để đổi lấy các quà tặng có sẵn trong hệ thống.

## Cấu trúc Database

### Bảng Gifts
- `Id`: ID quà tặng
- `Name`: <PERSON><PERSON><PERSON> quà tặng
- `Description`: <PERSON><PERSON> tả quà tặng
- `Img`: <PERSON><PERSON><PERSON> ảnh quà tặng
- `Points`: Số điểm cần để đổi
- `Status`: Trạng thái (1: Ho<PERSON><PERSON> động, 0: Kh<PERSON>ng hoạt động)
- `DateCreated`: <PERSON><PERSON><PERSON> tạo
- `DateExpired`: <PERSON><PERSON><PERSON> hết hạn
- `Quantity`: Số lượng còn lại
- `Category`: Danh mục quà tặng

### Bảng ExchangeGifts
- `Id`: ID giao dịch đổi quà
- `GiftId`: ID quà tặng
- `CustomerId`: ID khách hàng
- `Points`: S<PERSON> điểm đã sử dụng
- `Status`: <PERSON>r<PERSON><PERSON> thái (0: <PERSON><PERSON> chờ duyệt, 1: <PERSON><PERSON> duyệt, 2: Từ chối)
- `DateCreated`: Ngày tạo
- `DateApproved`: Ngày duyệt
- `Note`: Ghi chú

### Bảng HistoryReward (Sử dụng để tính điểm)
- `Id`: ID lịch sử
- `CustomerId`: ID khách hàng
- `Value`: Giá trị điểm (có thể âm hoặc dương)
- `Description`: Mô tả
- `Type`: Loại giao dịch
- `DateCreated`: Ngày tạo

## Cấu trúc File

### Data Access Layer
- `src/modules/gift/giftDA.tsx`: Xử lý API cho quà tặng và đổi quà

### Components
- `src/components/Gift/GiftCard.tsx`: Card hiển thị quà tặng
- `src/components/Gift/ExchangeHistoryCard.tsx`: Card hiển thị lịch sử đổi quà
- `src/components/Gift/GiftSkeleton.tsx`: Loading skeleton

### Screens
- `src/Screen/Page/GiftExchange.tsx`: Trang chính với 2 tabs
- `src/Screen/Page/GiftDetail.tsx`: Trang chi tiết quà tặng

### Navigation
- Đã thêm `GiftExchange` và `GiftDetail` vào `RootScreen` enum
- Đã thêm routes vào `EComStackNavigator`

## Tính năng chính

### 1. Tab "Đổi quà"
- Hiển thị danh sách quà tặng có sẵn
- Filter theo danh mục
- Hiển thị số điểm cần thiết
- Kiểm tra điều kiện đổi quà (đủ điểm, còn hàng, chưa hết hạn)
- Chức năng đổi quà với xác nhận

### 2. Tab "Chờ duyệt"
- Hiển thị các giao dịch đổi quà đang chờ admin duyệt (Status = 0)
- Badge hiển thị số lượng giao dịch chờ duyệt
- Không có filter (chỉ hiển thị status = 0)
- Hiển thị thông tin chi tiết từng giao dịch

### 3. Tab "Lịch sử"
- Hiển thị lịch sử tất cả các lần đổi quà
- Filter theo trạng thái (Tất cả, Đang chờ duyệt, Đã duyệt, Từ chối)
- Hiển thị thông tin chi tiết từng giao dịch

### 4. Tính năng chung
- Hiển thị số điểm hiện tại với gradient background
- Loading states với skeleton
- Pull to refresh
- Load more (pagination)
- Empty states
- Error handling
- Tab icons và badge cho số lượng chờ duyệt

## Cách sử dụng

### 1. Truy cập chức năng
- Vào trang Shop (từ bottom navigation)
- Chọn menu "Đổi quà" trong phần HaveShop

### 2. Đổi quà
1. Chọn tab "Đổi quà" (tab đầu tiên)
2. Duyệt qua các quà tặng có sẵn
3. Chọn quà tặng muốn đổi
4. Kiểm tra điều kiện (đủ điểm, còn hàng, chưa hết hạn)
5. Nhấn "Đổi ngay" và xác nhận
6. Chờ admin duyệt

### 3. Xem giao dịch chờ duyệt
1. Chọn tab "Chờ duyệt" (tab thứ hai)
2. Xem các giao dịch đang chờ admin duyệt
3. Badge hiển thị số lượng giao dịch chờ duyệt

### 4. Xem lịch sử
1. Chọn tab "Lịch sử" (tab thứ ba)
2. Xem tất cả các giao dịch đã thực hiện
3. Filter theo trạng thái nếu cần

## API Methods

### GiftDA Class
- `getGifts(page, size, category)`: Lấy danh sách quà tặng
- `getGiftDetail(giftId)`: Lấy chi tiết quà tặng
- `getCurrentPoints(customerId)`: Lấy số điểm hiện tại
- `exchangeGift(giftId, customerId, points)`: Đổi quà
- `getExchangeHistory(customerId, page, size, status)`: Lấy lịch sử đổi quà
- `getGiftCategories()`: Lấy danh sách danh mục

## Testing

### Test File
- `src/test/GiftExchangeTest.tsx`: Component test các chức năng chính

### Cách test
1. Import GiftExchangeTest component
2. Render component trong app
3. Nhấn các nút test để kiểm tra từng chức năng
4. Xem console log và snackbar để kiểm tra kết quả

## Lưu ý kỹ thuật

### 1. State Management
- Sử dụng local state với useState
- Không cần Redux vì dữ liệu không chia sẻ giữa nhiều component

### 2. Performance
- Sử dụng FlatList với pagination
- Skeleton loading cho UX tốt hơn
- Lazy loading cho images

### 3. Error Handling
- Try-catch cho tất cả API calls
- Hiển thị error messages thân thiện
- Fallback UI cho trường hợp lỗi

### 4. UI/UX
- Tuân theo design system hiện có
- Sử dụng ColorThemes và TypoSkin
- Responsive design
- Loading states và empty states

## Cấu hình cần thiết

### 1. Database
- Tạo bảng Gifts với cấu trúc như mô tả
- Tạo bảng ExchangeGifts với cấu trúc như mô tả
- Đảm bảo bảng HistoryReward đã tồn tại

### 2. API Endpoints
- Đảm bảo DataController có thể truy cập các bảng trên
- Cấu hình permissions phù hợp

### 3. Icons
- Đã thêm icon `giftExchange` vào iconSvg class

## Mở rộng tương lai

### 1. Push Notifications
- Thông báo khi đổi quà thành công
- Thông báo khi admin duyệt/từ chối

### 2. Advanced Features
- Wishlist quà tặng
- Rating và review quà tặng
- Chia sẻ quà tặng

### 3. Admin Panel
- Quản lý quà tặng
- Duyệt/từ chối đổi quà
- Báo cáo thống kê

## UI Design Updates

### Horizontal Gift Card Layout
- **Layout**: Horizontal card với image bên trái (80x80px)
- **Content**: Title, SL (số lượng), điểm và button "ĐỔI NGAY"
- **Button**: Orange background (#FFA500) với text trắng
- **Responsive**: Tự động điều chỉnh theo content
- **Status**: Hiển thị trạng thái (Chờ duyệt, Hết hàng, Không đủ điểm)

### Key Visual Elements
- **Diamond Icon**: Màu vàng (#FFA500) cho điểm
- **Compact Info**: Format "SL: 02" cho số lượng
- **Action Button**: "ĐỔI NGAY" thay vì "Đổi ngay"
- **Card Spacing**: 12px padding, 8px border radius cho image
