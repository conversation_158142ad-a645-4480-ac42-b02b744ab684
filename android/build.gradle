buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
        googlePlayServicesAuthVersion = "20.7.0" // <--- use this version or newer
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.google.gms:google-services:4.4.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url("$rootDir/../node_modules/react-native-vnpay-merchant/android/repo")
        }
        // Add Notifee local repository
        maven {
            url("$rootDir/../node_modules/@notifee/react-native/android/libs")
        }
        // Add JitPack repository
        maven { url "https://jitpack.io" }
        // Add Sonatype snapshots repository
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots/"
        }
    }
}
apply plugin: "com.facebook.react.rootproject"
