import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {NewsEvent} from '../models/newsEvent';
import {getImage} from './rootAction';

export const newsEventAction = {
  fetch: async (config: {
    page?: number;
    size?: number;
    searchRaw?: string;
    sortby?: {prop: string; direction?: 'ASC' | 'DESC'}[];
    customerId?: string;
  }) => {
    const controller = new DataController('NewsEvent');
    try {
      const params: any = {
        page: config?.page ?? 1,
        size: config?.size ?? 2,
      };
      if (config?.searchRaw) params.searchRaw = config.searchRaw;
      if (config?.sortby) params.sortby = config.sortby;
      const res = await controller.aggregateList(params);

      if (res.code === 200 && res.data.length > 0) {
        let listData = await getImage({items: res.data});
        if (config?.customerId) {
          listData = await checkRegisterEvent(listData, config.customerId);
        }
        return {
          data: listData,
          totalCount: res.totalCount,
        };
      }
      return {
        data: [],
        totalCount: 0,
      };
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return {
        data: [],
        totalCount: 0,
      };
    }
  },
};

const checkRegisterEvent = async (events: NewsEvent[], customerId: string) => {
  const controller = new DataController('EventRegister');

  const res = await controller.aggregateList({
    searchRaw: `@CustomerId: {${customerId}} @NewsEventId: {${events
      .map(e => e.Id)
      .join('|')}}`,
  });
  const newData = events.map((e: NewsEvent) => {
    const checkEvent = res.data.some(
      (event: any) => event.NewsEventId === e.Id,
    );
    return {
      ...e,
      isRegistered: checkEvent ? true : false,
    };
  });
  return newData;
};

const fetchNewsEvents = createAsyncThunk<
  NewsEvent[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('newsEvent/fetchData', async (config, thunkAPI: any) => {
  const controller = new DataController('NewsEvent');
  const customerId = thunkAPI.getState().customer.data?.Id;

  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200 && res.data.length > 0) {
      let listData = await getImage({items: res.data});

      if (customerId) {
        listData = await checkRegisterEvent(listData, customerId);
      }
      return {
        data: listData,
        totalCount: res.totalCount,
      };
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const loadMoreNewsEvent = createAsyncThunk<
  NewsEvent[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('newsEvent/loadMoreNewsEvent', async (config, thunkAPI) => {
  console.log('start load more');
  const controller = new DataController('NewsEvent');
  const customerId = thunkAPI.getState().customer.data?.Id;

  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200 && res.data.length > 0) {
      let listData = await getImage({items: res.data});
      if (customerId) {
        listData = await checkRegisterEvent(listData, customerId);
      }
      return listData;
    }
    return [];
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchNewsEvents, loadMoreNewsEvent};
