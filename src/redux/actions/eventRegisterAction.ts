import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {createAsyncThunk} from '@reduxjs/toolkit';

const eventRegisterAction = {
  find: async (query: string) => {
    const controller = new DataController('EventRegister');
    const res = await controller.getListSimple({
      page: 1,
      size: 1000,
      query,
    });
    if (res.code === 200) {
      return res.data;
    }
    return [];
  },
  findOne: async (id: string) => {
    const controller = new DataController('EventRegister');
    const res = await controller.getById(id);
    return res.data;
  },
  create: async (data: any) => {
    try {
      const controller = new DataController('EventRegister');
      const dataSend = {
        ...data,
        Id: randomGID(),
        DateCreated: new Date().getTime(),
      };
      const res = await controller.add([dataSend]);
      if (res.code === 200) {
        return res.data;
      }
      return res;
    } catch (error: any) {
      showSnackbar({
        message: error.message || 'Đã có lỗi sảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  },
};

const registerEvent = createAsyncThunk<any, any>(
  'newsEvent/registerEvent',
  async (data: any, thunkAPI: any) => {
    const customerId = thunkAPI.getState().customer.data?.Id;
    const res = await eventRegisterAction.create({
      ...data,
      customerId,
    });
    if (res.code === 200) {
      return data.eventId;
    }
    return null;
  },
);

export {registerEvent, eventRegisterAction};
