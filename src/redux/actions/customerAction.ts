import {createAsyncThunk} from '@reduxjs/toolkit';
import {DataController} from '../../base/baseController';
import { TransactionStatus, TransactionType } from '../../Config/Contanst';

const getRankCustomer = createAsyncThunk(
  'customer/getRankCustomer',
  async (
    {
      Id,
    }: {Id: string},
    {rejectWithValue},
  ) => {
    try {
      let totalReward = 0;
      // Gọi tất cả API cùng lúc với Promise.all
      const [totalRewardRes, resSum, resRank] = await Promise.all([
        // 1. T<PERSON>h tổng điểm tất cả
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
        }),

        // 2. <PERSON><PERSON><PERSON> tổng điểm từ hoa hồng và nhiệm vụ
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${Id}} (@Type: [${TransactionType.hoahong}] | @Type: [${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
        }),

        // 3. Lấy thông tin config rank
        new DataController('ConfigRank').getAll(),
      ]);

      // Xử lý kết quả tổng điểm hiển thị
      if (totalRewardRes.code === 200 && totalRewardRes.data.length > 0) {
        totalReward = totalRewardRes.data[0].TotalReward || 0;
      }

      // Xử lý kết quả điểm để tính rank
      let totalScore = 0;
      if (resSum.code === 200 && resSum.data.length > 0) {
        totalScore = resSum.data[0].TotalReward || 0;
      }

      // Xử lý rank và xác định rank hiện tại
      if (resRank.code === 200 && resRank.data.length > 0) {
        const ranksData = resRank.data;

        // Sắp xếp ranks theo điểm số tăng dần
        const sortedRanks = [...ranksData].sort(
          (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
        );

        // Tìm rank hiện tại dựa trên điều kiện
        let achievedRank = null;

        for (const rank of sortedRanks) {
          const requiredScore = parseFloat(rank.Score);

          // Kiểm tra điều kiện điểm số
          if (totalScore >= requiredScore) {
            achievedRank = rank;
          }
        }
        console.log('achievedRank', achievedRank);
        console.log('totalReward', totalReward);
        console.log('totalScore', totalScore);
        return {
          totalReward,
          totalScore,
          achievedRank,
        };
      } else {
        return {
          totalReward,
          totalScore,
          achievedRank: null,
        };
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      return rejectWithValue(error);
    }
  },
);

export {getRankCustomer};
