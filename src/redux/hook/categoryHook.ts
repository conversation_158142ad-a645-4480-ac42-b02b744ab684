import {useDispatch} from 'react-redux';
import {AppDispatch} from '../store/store';
import {CategoryStoreState, setData} from '../reducers/CategoryReducer';

export const useCategoryHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: <K extends keyof CategoryStoreState>(
      stateName: K,
      data: CategoryStoreState[K],
    ) => {
      dispatch(setData({stateName, data}));
    },
  };

  return action;
};
