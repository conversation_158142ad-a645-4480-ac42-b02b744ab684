import {PayloadAction, createSlice} from '@reduxjs/toolkit';

import {NotificationItem} from '../models/notification';
import {fetchNotifications} from '../actions/notificationAction';

const data: NotificationItem[] = [
  // Hôm nay
  {
    Id: '1',
    Name: '<PERSON>ia<PERSON> dịch đổi quà',
    DateCreated: 1750145116111,
    Sort: 1,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duy<PERSON>, bạn bị trừ (-900$)',
    Status: 0, // unread
    Type: 1,
    CustomerId: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
  {
    Id: '2',
    Name: 'Nhận hoa hồng',
    DateCreated: 1750145116111,
    Sort: 2,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "<PERSON><PERSON><PERSON><PERSON>"',
    Status: 1, // read
    Type: 2,
    CustomerId: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
  // <PERSON><PERSON><PERSON> hôm trước
  {
    Id: '3',
    Name: '<PERSON><PERSON><PERSON><PERSON> hoa hồng',
    DateCreated: 1750145116111,
    Sort: 3,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "Nguyễn Thanh Tùng"',
    Status: 0, // unread
    Type: 3,
    CustomerId: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
  {
    Id: '4',
    Name: 'Giao dịch đổi quà',
    DateCreated: 1750145116111,
    Sort: 4,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duyệt, bạn bị trừ (-900$)',
    Status: 1, // read
    Type: 1,
    CustomerId: '1',
    LinkWeb: '1',
    LinkApp: '1',
  },
];

interface notificationSimpleResponse {
  data: Array<NotificationItem>;
  totalCount: number;
  loading: boolean;
}

export type {notificationSimpleResponse};

const initState: notificationSimpleResponse = {
  data: data,
  totalCount: 0,
  loading: false,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState: initState,
  reducers: {
    setData: <K extends keyof notificationSimpleResponse>(
      state: notificationSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: notificationSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchNotifications.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchNotifications.fulfilled, (state, action) => {
      state.data = action.payload;
      state.loading = false;
    });
  },
});

export const {setData} = notificationSlice.actions;

export default notificationSlice.reducer;
