import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {Brand} from '../models/brand';
import {fetchBrands} from '../actions/brandAction';

interface BrandStoreState {
  data: Array<Brand>;
  loading: boolean;
  showDrawer: boolean;
}

export type {BrandStoreState};

const initState: BrandStoreState = {
  data: [],
  loading: false,
  showDrawer: false,
};

export const brandSlice = createSlice({
  name: 'brand',
  initialState: initState,
  reducers: {
    setData: <K extends keyof BrandStoreState>(
      state: BrandStoreState,
      action: PayloadAction<{
        stateName: K;
        data: BrandStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: (state: BrandStoreState, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    onFetchDone: (state: BrandStoreState) => {
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchBrands.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchBrands.fulfilled, (state, action) => {
      state.loading = false;
      state.data = action.payload;
    });
    builder.addCase(fetchBrands.rejected, state => {
      state.loading = false;
    });
  },
});

export const {setData, onFetching, onFetchDone} = brandSlice.actions;

export default brandSlice.reducer;
