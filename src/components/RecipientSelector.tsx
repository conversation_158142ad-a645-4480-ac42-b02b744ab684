import React, { memo } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import LinearGradient from 'react-native-linear-gradient';

interface RecipientSelectorProps {
  recipientName?: string;
  recipientPhone?: string;
  onPress: () => void;
}

const RecipientSelector: React.FC<RecipientSelectorProps> = memo(({
  recipientName,
  recipientPhone,
  onPress,
}) => {
  //tạo hàm ẩn số điện thoải
  const hidePhone = (phone: string) => {
    if (!phone) return '';
    return phone.replace(/(\d{3})(\d{3})(\d{3})/, '$1***$3');
  };

  // Debug log để kiểm tra props
  React.useEffect(() => {
    console.log('RecipientSelector props updated:', { recipientName, recipientPhone });
  }, [recipientName, recipientPhone]);

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <LinearGradient
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
        style={styles.container}
      >
<View style={styles.content}>
        <View style={styles.iconContainer}>
          <Winicon 
            src="outline/files/file-user" 
            size={24} 
            color={ColorThemes.light.neutral_text_title_color             
            } 
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.label}>Người nhận</Text>
          {recipientName && recipientPhone ? (
            <Text style={styles.recipient}>
              {recipientName}{recipientPhone ? `, ${hidePhone(recipientPhone)}` : ''}
            </Text>
          ) : (
            <Text style={styles.placeholder}>Chọn người nhận</Text>
          )}
        </View>
        <Winicon 
          src="outline/arrows/right-arrow" 
          size={20} 
          color={ColorThemes.light.neutral_text_subtitle_color} 
        />
      </View>
      </LinearGradient>

    </TouchableOpacity>
  );
});

RecipientSelector.displayName = 'RecipientSelector';

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    // padding: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.primary_main_color,
    marginBottom: 4,
  },
  recipient: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  phone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 2,
  },
  placeholder: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default RecipientSelector;
