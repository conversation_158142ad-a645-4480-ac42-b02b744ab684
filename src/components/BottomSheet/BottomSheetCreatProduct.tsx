import React, { forwardRef, useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../redux/store/store';
import { DataController } from '../../base/baseController';
import { ColorThemes } from '../../assets/skin/colors';
import { hideBottomSheet } from 'wini-mobile-components';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import ListItemLable from '../Product/LabelProduct';
import ListItem from '../Product/list/ListProductCreate';
import ScreenHeader from '../../Screen/Layout/header';

export const BottomSheetCreatProduct = forwardRef(
  function BottomSheetCreatProduct(
    data: {
      handleSelect?: any;
      handleSelectLabel?: any;
      handleSelectFrom?: any;
      type: any;
    },
    ref: any,
  ) {
    const dispatch: AppDispatch = useDispatch();
    const { type, handleSelect, handleSelectLabel, handleSelectFrom } = data;
    const BrandController = new DataController('Brand');
    const CategoryController = new DataController('Category');
    const [selecChildID, setSelecChildID] = useState<string>('');
    const [selecChildName, setSelecChildName] = useState<string>('');
    const [dataLabel, setDataLabel] = useState<any[]>();
    const [dataProduct, setDataProduct] = useState<any>();
    const [searchData, setSearchData] = useState<string>('');
    const [selectItemChild, setSelectItemChild] = useState<any>();
    const [backSelect, setBackSelect] = useState<boolean>(false);

    const { data: dataCategory } = useSelector(
      (state: RootState) => state.category,
    );

    // Filter data based on search
    const filteredDataLabel = useMemo(() => {
      if (!dataLabel || !searchData.trim()) return dataLabel || [];
      return dataLabel.filter((item: any) =>
        item.Name?.toLowerCase().includes(searchData.toLowerCase()),
      );
    }, [dataLabel, searchData]);

    const filteredDataCategory = useMemo(() => {
      if (!dataCategory || !searchData.trim()) return dataCategory || [];
      return dataCategory.filter((item: any) =>
        item.Name?.toLowerCase().includes(searchData.toLowerCase()),
      );
    }, [dataCategory, searchData]);

    useEffect(() => {
      console.log('check-dataCategory', dataCategory);
    }, [dataCategory]);

    const handleSubmit = (selecChildID: string, selecChildName: string) => {
      console.log('check-selecChildID', selecChildID);
      console.log('check-selecChildName', selecChildName);
      if (!selecChildID || !selecChildName) {
        return;
      }
      if (selecChildID) {
        if (type === 'label') {
          handleSelectLabel({
            id: selecChildID,
            name: selecChildName,
          });
          hideBottomSheet(ref);
        } else if (type === 'Product') {
          handleSelect({
            id: selecChildID,
            name: selecChildName,
          });
          hideBottomSheet(ref);
        }
      }
    };

    const callApiBrand = async () => {
      let respone = await BrandController.aggregateList({
        page: 1,
        size: 10,
      });
      if (respone && respone?.code == 200) {
        setDataLabel(respone?.data);
      }
    };

    const callApiCategory = async () => {
      let respone = await CategoryController.getPatternList({
        page: 1,
        size: 10,
        pattern: {
          ParentId: ['Id', 'Name'],
        },
      });
      if (respone && respone?.code == 200) {
        let arrayData = respone?.data?.map((item: any) => {
          return {
            ...item,
            Customer: respone?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            parent: respone?.Parent?.find(
              (parent: any) => parent.Id == item.ParentId,
            ),
          };
        });
        setDataProduct(arrayData);
      }
    };

    useEffect(() => {
      if (type === 'label') {
        callApiBrand();
      }
      if (type === 'Product') {
        callApiCategory();
      }
    }, [type]);

    const handleBackSelect = () => {
      hideBottomSheet(ref);
      setSelectItemChild(null);
    };
    const handleBackSelectLabel = () => {
      setSelectItemChild(null);
      setSelecChildID('');
      setSelecChildName('');
      setBackSelect(true);
    };



    return (
      <Pressable
        style={{
          width: '100%',
          height: Dimensions.get('window').height - 100,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          position: 'relative',
        }}>
        <ScreenHeader
          onBack={handleBackSelectLabel}
          title={type && type == 'label' ? `Chọn thương hiệu` : `Chọn danh mục`}
        />
        <KeyboardAvoidingView
          style={{ flex: 1, position: 'relative' }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
          keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
        >
          <View
            style={{
              padding: 10,
              backgroundColor: '#fff',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 10,
              }}>
              <TextInput
                style={{
                  flex: 1,
                  backgroundColor: '#f0f0f0',
                  borderRadius: 5,
                  padding: 8,
                  marginRight: 10,
                }}
                placeholder="Search"
                value={searchData}
                onChange={e => setSearchData(e.nativeEvent.text)}
              />

              <TouchableOpacity onPress={() => setSearchData('')}>
                <Text
                  style={{
                    color: '#007AFF',
                    fontSize: 16,
                  }}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{ height: 1000, marginBottom: 50 }}>
            {type == 'label' ? (
              <>
                <ListItemLable
                  setSelecChildID={setSelecChildID}
                  setSelecChildName={setSelecChildName}
                  dataLabel={filteredDataLabel}
                />
                {searchData.trim() && filteredDataLabel.length === 0 && (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <Text style={{ color: '#666', fontSize: 16 }}>
                      Không tìm thấy thương hiệu phù hợp
                    </Text>
                  </View>
                )}
              </>
            ) : null}
            {type == 'Product' ? (
              <>
                <ListItem
                  setSelecChildID={setSelecChildID}
                  setSelecChildName={setSelecChildName}
                  selectItemChild={selectItemChild}
                  setSelectItemChild={setSelectItemChild}
                  dataCategory={filteredDataCategory}
                  backSelect={backSelect}
                  setBackSelect={setBackSelect}
                />
                {searchData.trim() && filteredDataCategory.length === 0 && (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <Text style={{ color: '#666', fontSize: 16 }}>
                      Không tìm thấy danh mục phù hợp
                    </Text>
                  </View>
                )}
              </>
            ) : null}
            <Pressable style={{ flex: 1 }}></Pressable>
          </View>
          <View
            style={{
              flex: 1,
              marginBottom: 10,
              position: 'absolute',
              bottom: 2,
              width: '100%',
            }}>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'space-around',
                position: 'absolute',
                bottom: 30,
              }}>
              <TouchableOpacity style={styles.cancelButton}>
                <Text
                  style={styles.buttonText}
                  onPress={() => hideBottomSheet(ref)}>
                  Đóng
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={() => handleSubmit(selecChildID, selecChildName)}>
                <Text style={styles.buttonTextConfirm}>Xác nhận</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    );
  },
);

const styles = StyleSheet.create({
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
  },
  confirmButton: {
    backgroundColor: ColorThemes.light.primary_main_color,// Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
  },
  buttonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  buttonTextConfirm: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default BottomSheetCreatProduct;
