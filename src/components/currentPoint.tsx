import { ImageBackground, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { AppButton, AppSvg } from "wini-mobile-components";
import { Ultis } from "../utils/Utils";
import iconSvg from "../svg/icon";
import { TypoSkin } from "../assets/skin/typography";
import { ColorThemes } from "../assets/skin/colors";
import { useEffect, useState } from "react";
import GiftDA from "../modules/gift/giftDA";
import { useSelectorCustomerState } from "../redux/hook/customerHook";

const CurrentPoint = (pros: any) => {
    const [currentPoints, setCurrentPoints] = useState(0);
    const [loading, setLoading] = useState(false);
    const customer = useSelectorCustomerState().data;
    const giftDA = new GiftDA();
    const fetchCurrentPoints = async () => {
        if (!customer?.Id) return;
        try {
            const points = await giftDA.getCurrentPoints(customer.Id);
            setCurrentPoints(points);
        } catch (error) {
            console.error('Error fetching current points:', error);
        }
    };
    useEffect(() => {
        fetchCurrentPoints();
    }, []);
    return (
        <View
            style={styles.pointsContainer}>
            <ImageBackground
                source={require('../assets/Line-cate.png')}
                imageStyle={{ resizeMode: 'contain' }}
            >
                <View style={styles.pointsContent}>
                    <Text style={styles.pointsLabel}>Bạn đang có</Text>
                    <View style={styles.pointsIconContainer}>
                        <AppSvg SvgSrc={iconSvg.moneyIcon} size={24} />
                        <Text style={styles.pointsValue}>
                            {Ultis.money(currentPoints)} điểm
                        </Text>
                    </View>
                </View>
                {pros.enableButton && (
                    <View style={styles.buttonContainer}>
                        <AppButton
                            title="Đôi điểm"
                            onPress={() => {}}
                            backgroundColor={ColorThemes.light.primary_main_color}
                            containerStyle={styles.button}
                            borderColor='transparent'
                        />
                    </View>
                )}
            </ImageBackground>
        </View>
    );

};
const styles = StyleSheet.create({
    pointsContainer: {
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 16,
        height: 82,
    },
    pointsContent: {
        width: '100%',
        flexDirection: 'column',
        height: 82,
        paddingHorizontal: 70,
        paddingVertical: 12,

    },
    pointsIconContainer: {
        width: '100%',
        flexDirection: 'row',
        gap: 4,
    },
    pointsLabel: {
        ...TypoSkin.body1,
        color: ColorThemes.light.neutral_text_title_color,
        fontSize: 16,
        fontWeight: '500',
    },
    pointsValue: {
        ...TypoSkin.heading7,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',

    },
    buttonContainer: {
        width: '100%',
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    button: {
        borderRadius: 24,
        height: 48,
    },
});

export default CurrentPoint;