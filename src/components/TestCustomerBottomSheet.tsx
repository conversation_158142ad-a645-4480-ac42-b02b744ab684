import React, { useState, useRef } from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { showBottomSheet, FBottomSheet } from 'wini-mobile-components';
import CustomerBottomSheet from './CustomerBottomSheet';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';

interface Customer {
  Id: string;
  Name: string;
  Mobile?: string;
  Email?: string;
  AvatarUrl?: string;
}

const TestCustomerBottomSheet = () => {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const btsRef = useRef<any>(null);

  const handleSelectCustomer = (customer: Customer) => {
    console.log('Customer selected:', customer);
    setSelectedCustomer(customer);
  };

  const showCustomerBottomSheet = () => {
    showBottomSheet({
      ref: btsRef,
      title: 'Chọn khách hàng',
      enableDismiss: true,
      children: (
        <CustomerBottomSheet
          ref={btsRef}
          onSelectCustomer={handleSelectCustomer}
          selectedCustomerId={selectedCustomer?.Id}
        />
      ),
    });
  };

  return (
    <View style={styles.container}>
      <FBottomSheet ref={btsRef} />
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={showCustomerBottomSheet}
      >
        <Text style={styles.buttonText}>
          {selectedCustomer ? `Đã chọn: ${selectedCustomer.Name}` : 'Chọn khách hàng'}
        </Text>
      </TouchableOpacity>

      {selectedCustomer && (
        <View style={styles.selectedInfo}>
          <Text style={styles.selectedTitle}>Khách hàng đã chọn:</Text>
          <Text style={styles.selectedName}>{selectedCustomer.Name}</Text>
          {selectedCustomer.Mobile && (
            <Text style={styles.selectedPhone}>{selectedCustomer.Mobile}</Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 20,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  selectedInfo: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  selectedTitle: {
    ...TypoSkin.heading6,
    marginBottom: 10,
  },
  selectedName: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    marginBottom: 5,
  },
  selectedPhone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default TestCustomerBottomSheet;
