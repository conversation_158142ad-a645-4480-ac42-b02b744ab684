import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Modal,
} from 'react-native';
import {
  Winicon,
  FDialog,
  showDialog,
  ComponentStatus,
} from 'wini-mobile-components';
import { Title } from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import { TypoSkin } from '../../../assets/skin/typography';
import { ColorThemes } from '../../../assets/skin/colors';
import { Ultis } from '../../../utils/Utils';

const CardOrder = ({
  item,
  index,
  action,
  handleUpdateStatusProcessOrder,
  handleViewDetailOrder
}: {
  item: any;
  index: number;
  action?: string;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void;
  handleViewDetailOrder: (item: any) => void;
}) => {
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const dialogRef = useRef<any>(null);

  // Danh sách các trạng thái có thể chọn
  const statusOptions = [
    { id: 1, name: 'Đang xử lý', type: 'processing' },
    { id: 3, name: 'Hoàn thành', type: 'completed' },
    { id: 4, name: 'Hủy', type: 'cancelled' },
  ];

  // Lọc trạng thái dựa trên trạng thái hiện tại của item
  const getFilteredStatusOptions = () => {
    if (item?.Status === 1) {
      return statusOptions.filter(status => status.type === 'processing');
    } else if (item?.Status === 2) {
      return statusOptions.filter(status => status.type === 'completed');
    }
    return statusOptions;
  };

  const handleStatusPress = () => {
    setSelectedStatus(null); // Reset selection when opening modal
    setShowStatusPopup(true);
  };

  const handleCancelPress = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Xác nhận hủy đơn hàng',
      content: `Bạn có chắc chắn muốn hủy đơn hàng #${item?.Code}?`,
      onSubmit: async () => {
        handleUpdateStatusProcessOrder(item, 'cancelled');
      },
    });
  };

  const handleStatusSelect = (statusType: string) => {
    setSelectedStatus(statusType);
  };

  const handleUpdateStatus = async () => {
    if (selectedStatus) {
      await handleUpdateStatusProcessOrder(item, selectedStatus);
      setShowStatusPopup(false);
      setSelectedStatus(null);
    }
  };

  const handleToggleProducts = () => {
    setShowAllProducts(!showAllProducts);
  };

  // Lấy danh sách sản phẩm để hiển thị
  const getDisplayProducts = () => {
    if (!item?.orderDetails || item.orderDetails.length === 0) return [];
    return showAllProducts ? item.orderDetails : [item.orderDetails[0]];
  };

  const getTotalRefund = () => {
    return !item?.Refund
      ? 0
      : item.orderDetails?.reduce(
        (total: number, orderDetail: any) =>
          total +
          (JSON.parse(orderDetail?.Refund ?? '{}')?.f0 ?? 0) +
          (JSON.parse(orderDetail?.Refund ?? '{}')?.f1 ?? 0) +
          (JSON.parse(orderDetail?.Refund ?? '{}')?.f2 ?? 0),
        0,
      );
  };

  return (
    <TouchableOpacity onPress={() => handleViewDetailOrder(item)}>
      <FDialog ref={dialogRef} />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={{ ...styles.orderId, gap: 2, flexDirection: 'row' }}>
            {'Đơn hàng '}
            <Text style={{ color: '#000', fontWeight: 'bold' }}>
              #{item?.Code}
            </Text>
          </Text>
          <Text
            style={
              item?.Status == 3
                ? styles.statusDone
                : item?.Status == 2 || item?.Status == 1
                  ? styles.statusProcessing
                  : styles.status
            }>
            {item?.Status == 1 && 'Đang thực hiện'}
            {item?.Status == 2 && 'Đang thực hiện'}
            {item?.Status == 3 && 'Hoàn thành'}
            {item?.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        {getDisplayProducts().map((productItem: any, index: number) => (
          <View
            style={styles.productContainer}
            key={`${index}-${productItem?.Id}`}>
            <Image
              source={{ uri: ConfigAPI.urlImg + productItem?.productInfo?.Img }}
              style={styles.productImage}
            />
            <View style={styles.productInfo}>
              <Text style={styles.productName}>
                {productItem?.productInfo?.Name}
              </Text>
              <Text style={styles.productDetails}>
                {
                  <>
                    <Text style={styles.productName}>Hoàn tiền :</Text>
                    <Text style={styles.productDetails}>
                      <Text>
                        (kh:{' '}
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 0,
                          )?.Value ?? 0,
                        )}
                        đ)-(f1:
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 1,
                          )?.Value ?? 0,
                        )}
                        đ)-(f2:
                        {Ultis.money(
                          productItem.historyReward?.find(
                            (history: any) => history.Filial === 2,
                          )?.Value ?? 0,
                        )}
                        đ)
                      </Text>
                    </Text>{' '}
                  </>
                }
              </Text>
              <Text style={styles.productPrice}>
                <Text style={styles.productName}>
                  Số lượng: {productItem?.Quantity}
                </Text>
              </Text>
              <Text style={styles.productPrice}>
                <Text style={styles.productName}>Giá:</Text>
                <Text style={{ color: ColorThemes.light.error_main_color }}>
                  {Ultis.money(
                    (productItem?.Price ?? 0) *
                    (1 - (productItem?.Discount ?? 0) / 100),
                  )}{' '}
                  VNĐ
                </Text>
              </Text>
            </View>
          </View>
        ))}

        {/* Số lượng và tổng tiền */}
        <View style={styles.quantityTotal}>
          {item?.orderDetails?.length > 1 && (
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={handleToggleProducts}>
              <Text style={styles.quantityText}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {showAllProducts ? 'Thu gọn' : 'Xem thêm'}
                </Text>
                <Winicon
                  src={
                    showAllProducts
                      ? 'color/arrows/arrow-sm-up'
                      : 'color/arrows/arrow-sm-down'
                  }
                  size={13}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </Text>
            </TouchableOpacity>
          )}
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={styles.quantity}>
                Tổng hoàn ({item?.orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <View style={{ width: 30 }}></View>
              <Text style={styles.money}>
                {Ultis.money(getTotalRefund())} VNĐ
              </Text>
            </View>
          </View>
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={styles.quantity}>
                Tổng tiền ({item?.orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <View style={{ width: 30 }}></View>
              <Text style={styles.money}>{Ultis.money(item?.Value)} VNĐ</Text>
            </View>
          </View>
        </View>
        {action && action !== Title.Cancel && item?.Status !== 3 && (
          <View style={styles.button}>
            <View style={{ flexDirection: 'row', gap: 10 }}></View>
            <View style={{ flexDirection: 'row', gap: 10 }}>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleStatusPress}>
                <Text style={styles.confirmButtonText}>
                  Cập nhật trạng thái
                </Text>
              </TouchableOpacity>
              {item?.Status == 2 && (
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleCancelPress}>
                  <Text style={styles.confirmButtonText}>Xác nhận hủy</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
      <Modal
        visible={showStatusPopup}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowStatusPopup(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Cập nhật đơn hàng #
                <Text style={{ fontWeight: 'bold' }}>{item?.Code}</Text>
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowStatusPopup(false)}>
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>

            {/* Order Info */}
            <View style={styles.orderInfoContainer}></View>
            <View style={styles.statusList}>
              {getFilteredStatusOptions().map(status => (
                <TouchableOpacity
                  key={status.id}
                  style={[
                    styles.statusOption,
                    selectedStatus === status.type && styles.selectedStatus,
                  ]}
                  onPress={() => handleStatusSelect(status.type)}>
                  <View style={styles.statusOptionContent}>
                    <View
                      style={[
                        styles.statusIndicator,
                        selectedStatus === status.type &&
                        styles.selectedIndicator,
                      ]}
                    />
                    <Text
                      style={[
                        styles.statusOptionText,
                        selectedStatus === status.type &&
                        styles.selectedStatusText,
                      ]}>
                      {status.name}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtonsContainer}>
              <View style={styles.additionalButtons}>
                <TouchableOpacity
                  style={[
                    styles.additionalButton,
                    !selectedStatus && styles.disabledButton,
                  ]}
                  onPress={() => handleUpdateStatus()}
                  disabled={!selectedStatus}>
                  <Text
                    style={[
                      styles.additionalButtonText,
                      !selectedStatus && styles.disabledButtonText,
                    ]}>
                    Cập nhật trạng thái
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    padding: 10,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2, // Bóng cho Android
    marginTop: 6,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  orderId: {
    flex: 0.6,
    ...TypoSkin.title2,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 14,
  },
  status: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#DA251D',
    fontSize: 14,
  },
  statusProcessing: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#FA8C16',
    fontSize: 14,
  },
  statusDone: {
    flex: 0.3,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: ColorThemes.light.success_main_color,
    fontSize: 14,
  },
  productContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  productImage: {
    borderWidth: 5,
    borderRadius: 50,
    width: 60,
    height: 60,
    marginRight: 16,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.title4,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
  },
  productDetails: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginVertical: 2,
  },
  productPrice: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  quantityTotal: {
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: 10,
  },
  quantityButton: {
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  quantityText: {
    fontSize: 14,
    color: '#555',
    display: 'flex',
    gap: 1,
  },
  quantityDetail: {
    height: 30,
    width: '100%',
    alignItems: 'flex-end',
  },
  quantity: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  money: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: 'bold',
    fontSize: 14,
  },
  button: {
    minHeight: 40,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
  },
  confirmButton: {
    backgroundColor: '#FFC043',
    height: 35,
    width: 154,
    borderRadius: 50,
    justifyContent: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 20,
    width: '100%',
    maxWidth: 420,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.35,
    shadowRadius: 35,
    elevation: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    flex: 1,
    fontSize: 18,
    lineHeight: 24,
  },
  modalMessage: {
    ...TypoSkin.title4,
    color: '#666666',
    marginTop: 8,
    lineHeight: 22,
    fontSize: 14,
  },
  statusList: {
    width: '100%',
    marginBottom: 28,
    marginTop: 20,
  },
  statusOption: {
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: '#f0f0f0',
    borderRadius: 16,
    marginBottom: 12,
    backgroundColor: '#fafbfc',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
    overflow: 'hidden',
  },
  selectedStatus: {
    backgroundColor: '#fff8e1',
    borderColor: '#ffc107',
    borderWidth: 2,
    shadowColor: '#ffc107',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    transform: [{ scale: 1.02 }],
  },
  statusOptionText: {
    ...TypoSkin.title3,
    color: '#333333',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 20,
  },
  selectedStatusText: {
    color: '#e65100',
    fontWeight: '700',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cancelButtonText: {
    ...TypoSkin.title3,
    color: '#666666',
    fontWeight: '600',
    fontSize: 16,
  },
  confirmModalButton: {
    backgroundColor: '#ff6b35',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#ff6b35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  confirmModalButtonText: {
    ...TypoSkin.title3,
    color: '#ffffff',
    fontWeight: '700',
    fontSize: 16,
  },
  additionalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  additionalButton: {
    backgroundColor: '#ffc107',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 14,
    alignItems: 'center',
    flex: 1,
    shadowColor: '#ffc107',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  additionalButtonText: {
    ...TypoSkin.title3,
    color: '#b71800',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  closeButtonText: {
    fontSize: 22,
    color: '#6c757d',
    fontWeight: '400',
    lineHeight: 22,
    textAlign: 'center',
  },
  orderInfoContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  orderInfoText: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    fontSize: 16,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 16,
    fontSize: 16,
  },
  statusOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: 'transparent',
  },
  selectedIndicator: {
    backgroundColor: '#ffc107',
    borderColor: '#ffc107',
    shadowColor: '#ffc107',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  actionButtonsContainer: {
    marginTop: 8,
  },
  disabledButton: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
    shadowOpacity: 0,
    elevation: 0,
  },
  disabledButtonText: {
    color: '#999999',
    fontWeight: '500',
  },
});

export default CardOrder;
