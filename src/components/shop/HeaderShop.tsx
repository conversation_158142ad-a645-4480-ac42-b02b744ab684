/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Image} from 'react-native';
import {useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';

const HeaderBackground = () => {
  const [isVip, setIsVip] = useState(false);
  const {rankInfo} = useSelector((state: RootState) => state.customer);

  useEffect(() => {
    if (rankInfo?.achievedRank) {
      setIsVip(Number(rankInfo?.achievedRank?.Sort) > 1);
    }
  }, [rankInfo?.achievedRank]);
  return (
    <View style={styles.header}>
      {/* Header */}
      {isVip ? (
        <Image
          source={require('../../assets/images/header_vip.png')}
          style={styles.headerImage}
        />
      ) : (
        <Image
          source={require('../../assets/images/header_group.png')}
          style={styles.headerImage}
        />
      )}
    </View>
  );
};
export default HeaderBackground;

const styles = StyleSheet.create({
  header: {
    maxHeight: 70,
  },
  headerImage: {width: '100%', height: 120},
});
