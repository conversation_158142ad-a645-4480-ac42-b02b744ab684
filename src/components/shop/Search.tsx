import React from 'react';
import {View, TextInput, StyleSheet} from 'react-native';

const SearchBar = ({
  setDataSearch,
  placeholder = 'Bạn muốn tìm gì?',
}: {
  setDataSearch: (data: string) => void;
  placeholder?: string;
}) => {
  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        autoFocus={false}
        clearButtonMode="while-editing"
        clearTextOnFocus={false}
        autoCapitalize="none"
        autoCorrect={false}
        returnKeyType="done"
        placeholder={placeholder}
        placeholderTextColor="#999"
        onChange={e => setDataSearch(e.nativeEvent.text)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 11,
    paddingHorizontal: 20,
    borderWidth: 0.5,
    borderColor: '#999',
    marginVertical: 16,
    marginHorizontal: 16,
    lineHeight: 20,
  },
  input: {
    flex: 1,
    minHeight: 31,
    color: '#333',
    paddingVertical: 0,
    padding: 5,
    fontSize: 16,
  },
});

export default SearchBar;
