import React, { useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import ModalPromotion from './ModalPromotion';
import iconSvg from '../../../svg/icon';

const TestModal = () => {
  const [isShow, setIsShow] = useState(false);
  const [dataDiscount, setDataDiscount] = useState([
    {
      Id: 'test1',
      Name: 'Test Product 1',
      Price: 100000,
      InStock: 10,
      Discount: 0
    },
    {
      Id: 'test2', 
      Name: 'Test Product 2',
      Price: 200000,
      InStock: 5,
      Discount: 0
    }
  ]);

  const handleShow = () => {
    console.log('Test handleShow called');
    setIsShow(true);
  };

  const closeModal = () => {
    console.log('Test closeModal called');
    setIsShow(false);
  };

  const handleSelectAllData = (ref: any) => {
    console.log('Test handleSelectAllData called');
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.button} onPress={handleShow}>
        <Text style={styles.buttonText}>Test Modal</Text>
      </TouchableOpacity>
      
      <ModalPromotion
        isShow={isShow}
        closeModal={closeModal}
        svgSrc={iconSvg.updateAll}
        title="Test Modal Promotion"
        dataDiscount={dataDiscount}
        ref={null}
        handleSelectAllData={handleSelectAllData}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TestModal;
