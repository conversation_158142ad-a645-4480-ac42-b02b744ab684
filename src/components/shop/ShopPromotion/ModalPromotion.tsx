import React, {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {AppSvg, ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {TextFieldForm} from '../../../modules/news/form/component-form';
import {ColorThemes} from '../../../assets/skin/colors';
import {DataController} from '../../../base/baseController';
import {useNavigation} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ModalPromotion = ({
  isShow,
  closeModal,
  svgSrc,
  title,
  dataDiscount,
  handleSelectAllData,
  ref,
}: {
  isShow: boolean;
  closeModal: () => void;
  svgSrc: string;
  title: string;
  dataDiscount: any[];
  handleSelectAllData: (ref: any) => void;
  ref: any;
}) => {
  const [DiscountValue, setDiscountValue] = useState<any[]>([]);
  const [errText, setErrText] = useState<string>('');

  const productDA = new DataController('Product');

  const methods = useForm({shouldFocusError: false});
  let textFieldStyle = {
    height: 48,
    paddingLeft: 8,
    paddingRight: 8,
    borderWidth: 0,
  };

  useEffect(() => {
    console.log('check-dataDiscount', dataDiscount);
    if (isShow && dataDiscount && dataDiscount?.length > 0) {
      setDiscountValue(dataDiscount);
    } else {
      setDiscountValue([]);
    }
  }, [isShow]);

  const onSubmit = async (data: any) => {
    if (DiscountValue && DiscountValue?.length == 0) {
      closeModal();
      showSnackbar({
        message: 'không có sản phẩm nào theo danh mục bạn đã chọn',
        status: ComponentStatus.ERROR,
      });
    }
    if (DiscountValue && DiscountValue?.length == 1) {
      // Update the discount value with data.Content
      const updatedDiscountValue = [...DiscountValue];

      if (updatedDiscountValue[0]?.Children) {
        updatedDiscountValue[0] = {
          ...updatedDiscountValue[0],
          Discount: Number(data.Content),
          Children: updatedDiscountValue[0]?.Children.toString(),
        };
      } else {
        updatedDiscountValue[0] = {
          ...updatedDiscountValue[0],
          Discount: Number(data.Content),
        };
      }

      console.log('check-updatedDiscountValue', updatedDiscountValue);

      let response = await productDA.edit(updatedDiscountValue);
      console.log('check-response', response);
      if (response?.code === 200) {
        closeModal();
        methods.setValue('Content', '');
        handleSelectAllData(ref);
      }
    } else {
      for (const item of DiscountValue) {
        item.Discount = Number(data.Content);
      }
      let response = await productDA.edit(DiscountValue);
      console.log('check-response', response);
      if (response?.code === 200) {
        closeModal();
        setErrText('');
        methods.setValue('Content', '');
        handleSelectAllData(ref);
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isShow}
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Icon cảnh báo */}
            <AppSvg SvgSrc={svgSrc} size={60} />
            {/* Nội dung thông báo */}
            <Text style={styles.modalText}>{title}</Text>
            <View style={{width: '100%'}}>
              <Text style={{fontSize: 16, fontWeight: '600'}}>Nhập số %</Text>
              <View>
                <TextFieldForm
                  control={methods.control}
                  name="Content"
                  placeholder="Nhập số % đánh giá"
                  returnKeyType="done"
                  textFieldStyle={textFieldStyle}
                  errors={methods.formState.errors}
                  type="number-pad"
                  register={methods.register}
                  required
                  style={styles.input}
                />
              </View>
            </View>

            <View style={styles.buttonContainer}>
              {/* Nút Hủy */}
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={closeModal}
                activeOpacity={0.8}>
                <Text style={styles.cancelButtonText}>Đóng</Text>
              </TouchableOpacity>

              {/* Nút Đồng ý */}
              <TouchableOpacity
                style={[styles.button, styles.agreeButton]}
                onPress={errText ? undefined : methods.handleSubmit(onSubmit)}
                activeOpacity={0.8}>
                <Text style={styles.agreeButtonText}>Đồng ý</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  showButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  showButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    width: width * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  warningIcon: {
    width: 50,
    height: 50,
    backgroundColor: '#FF8C00',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  warningIconText: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  modalText: {
    fontSize: 20,
    marginTop: 12,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
    marginTop: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    color: 'black',
  },
  cancelButton: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  agreeButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  cancelButtonText: {
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 16,
    fontWeight: '600',
  },
  agreeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  input: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: ColorThemes.light.neutral_main_border_color,
    marginBottom: 16,
    marginTop: 4,
  },
});

export default ModalPromotion;
