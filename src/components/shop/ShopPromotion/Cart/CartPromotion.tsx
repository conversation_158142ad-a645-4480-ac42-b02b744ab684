import {Text} from 'react-native-paper';
import {Checkbox} from 'wini-mobile-components';
import {TypoSkin} from '../../../../assets/skin/typography';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Image, ScrollView, TouchableOpacity, View} from 'react-native';
import ConfigAPI from '../../../../Config/ConfigAPI';

export default function CartPromotion({
  item,
  index,
  handleSelectCategory,
  handleSelectAllCategory,
  selectTwo,
  getdataByMenuTwo,
  selectThree,
  handleGetDataDiscount,
  selectAll,
  selectedItems,
  isSelected,
  handleSelectChild,
  selectProduct,
  typeSelect,
  handleRemoveItem,
}: {
  item: any;
  index: number;
  handleSelectCategory: (item: any) => void;
  handleSelectAllCategory: (item: any) => void;
  selectTwo: boolean;
  getdataByMenuTwo: (item: any) => void;
  selectThree: boolean;
  handleGetDataDiscount: (data: any) => void;
  selectAll: boolean;
  selectedItems: Set<string>;
  isSelected: boolean;
  handleSelectChild?: (child: any) => void;
  selectProduct: boolean;
  typeSelect: boolean;
  handleRemoveItem?: (item: any) => void;
}) {
  // Kiểm tra xem danh mục này có children không
  const hasChildren = item?.Children && item?.Children.length > 0;

  // Kiểm tra xem item có phải là sản phẩm không (có Price và InStock)
  const isProduct = item?.Price && item?.InStock;

  // Kiểm tra xem item có phải là danh mục con không (có ParentId)
  const isChildCategory = item?.ParentId;

  // Kiểm tra xem item có phải là danh mục cha không (có Children)
  const isParentCategory = hasChildren && !item?.ParentId;

  console.log('check-selectThree ', selectThree);
  console.log('check-selectProduct ', selectProduct);

  return (
    <View
      key={`title-${index}`}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingBottom: 10,
        paddingTop: 10,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_main_background_color,
        marginLeft: 16,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: 15,
        }}>
        {!item?.Price && (
          <Checkbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        )}
        {item?.Price && !selectThree && !typeSelect && (
          <Checkbox
            value={selectProduct}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        )}
        {!selectProduct && selectThree && (
          <Checkbox
            value={selectThree}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        )}
        {typeSelect && (
          <Checkbox
            value={true}
            onChange={() => {
              if (handleRemoveItem) {
                handleRemoveItem(item);
              }
            }}
            size={24}
          />
        )}

        {/* Hiển thị ảnh cho sản phẩm */}
        {isProduct && (
          <Image
            source={{uri: ConfigAPI.urlImg + item?.Img}}
            style={{height: 46, width: 46, borderRadius: 50, marginLeft: 15}}
          />
        )}

        <Text
          style={{
            marginLeft: isProduct ? 14 : 14,
            ...TypoSkin.body2,
          }}>
          {item?.Name}
        </Text>
      </View>
      {selectAll ? (
        <View></View>
      ) : (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {/* Hiển thị mũi tên cho danh mục cha hoặc danh mục con */}
          {!selectTwo &&
            !selectThree &&
            (isParentCategory || isChildCategory) && (
              <TouchableOpacity
                style={{marginRight: 16}}
                onPress={() => handleSelectCategory(item)}>
                <FontAwesomeIcon
                  icon={faAngleRight}
                  color={ColorThemes.light.black}
                  size={16}
                />
              </TouchableOpacity>
            )}
          {selectTwo &&
            !selectThree &&
            (isParentCategory || isChildCategory) && (
              <TouchableOpacity
                style={{marginRight: 16}}
                onPress={() => getdataByMenuTwo(item)}>
                <FontAwesomeIcon
                  icon={faAngleRight}
                  color={ColorThemes.light.black}
                  size={16}
                />
              </TouchableOpacity>
            )}
        </View>
      )}
    </View>
  );
}
