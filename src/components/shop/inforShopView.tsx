import {ScrollView, Text, View} from 'react-native';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

export default function InforShopView() {
  const route = useRoute<any>();
  const shop = route?.params?.shop;
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title={'Thông tin cửa hàng'} />
      <ScrollView style={{flex: 1}}>
        {/* infor shop like Name, Rating, Total Product, Total Order */}
        <View
          style={{
            paddingHorizontal: 24,
            flex: 1,
            flexDirection: 'row',
            alignContent: 'center',
            justifyContent: 'space-between',
            borderBottomWidth: 1,
            borderBottomColor: ColorThemes.light.neutral_main_border_color,
            paddingBottom: 12,
          }}>
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>{shop?.Name ?? '-'}</Text>
            <Text style={{...TypoSkin.body3}}>Tên cửa hàng</Text>
          </View>
        </View>
        {/* add divider */}
        <View
          style={{
            height: 1,
            backgroundColor: ColorThemes.light.neutral_main_border_color,
          }}
        />
        <View
          style={{
            paddingHorizontal: 24,
            flex: 1,
            flexDirection: 'row',
            alignContent: 'center',
            justifyContent: 'space-between',
            borderBottomWidth: 1,
            borderBottomColor: ColorThemes.light.neutral_main_border_color,
            paddingBottom: 12,
          }}>
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>
              {shop?.rating?.toFixed(1) ?? '-'}
            </Text>
            <Text style={{...TypoSkin.body3}}>Đánh giá</Text>
          </View>
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>
              {shop?.totalProducts ?? '-'}
            </Text>
            <Text style={{...TypoSkin.body3}}>Sản phẩm</Text>
          </View>
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>{shop?.totalOrder ?? '-'}</Text>
            <Text style={{...TypoSkin.body3}}>Đã bán</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
