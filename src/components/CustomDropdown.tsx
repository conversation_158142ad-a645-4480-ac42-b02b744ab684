import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, FlatList, StyleSheet } from 'react-native';
import { Text as PaperText } from 'react-native-paper';
import { ColorThemes } from '../assets/skin/colors';

interface Option {
    id: string | number;
    name: string;
}

interface CustomDropdownProps {
    placeholder?: string;
    options: Option[];
    value?: string | number;
    onValueChange: (value: string | number) => void;
    label?: string;
    disabled?: boolean;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
    placeholder = 'Chọn tùy chọn',
    options,
    value,
    onValueChange,
    label,
    disabled = false,
}) => {
    const [isVisible, setIsVisible] = useState(false);

    const selectedOption = options.find(option => option.id === value);

    const handleSelect = (option: Option) => {
        onValueChange(option.id);
        setIsVisible(false);
    };

    return (
        <View style={styles.container}>
            {label && <PaperText style={styles.label}>{label}</PaperText>}

            <TouchableOpacity
                style={[
                    styles.input,
                    disabled && styles.disabledInput
                ]}
                onPress={() => !disabled && setIsVisible(true)}
                disabled={disabled}
            >
                <Text style={[
                    styles.inputText,
                    !selectedOption && styles.placeholderText
                ]}>
                    {selectedOption ? selectedOption.name : placeholder}
                </Text>
                <Text style={styles.dropdownIcon}>▼</Text>
            </TouchableOpacity>

            <Modal
                visible={isVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setIsVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setIsVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <FlatList
                            data={options}
                            keyExtractor={(item) => item.id.toString()}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={[
                                        styles.optionItem,
                                        selectedOption?.id === item.id && styles.selectedOption
                                    ]}
                                    onPress={() => handleSelect(item)}
                                >
                                    <Text style={[
                                        styles.optionText,
                                        selectedOption?.id === item.id && styles.selectedOptionText
                                    ]}>
                                        {item.name}
                                    </Text>
                                </TouchableOpacity>
                            )}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginTop: 16,
    },
    label: {
        marginBottom: 8,
        fontSize: 14,
        color: '#666',
    },
    input: {
        backgroundColor: 'white',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#D4E2F3',
        height: 50,
        paddingHorizontal: 16,
        fontSize: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    disabledInput: {
        backgroundColor: '#f5f5f5',
        opacity: 0.6,
    },
    inputText: {
        flex: 1,
        fontSize: 16,
        color: '#333',
    },
    placeholderText: {
        color: '#999',
    },
    dropdownIcon: {
        fontSize: 12,
        color: '#666',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 8,
        width: '80%',
        maxHeight: '60%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    optionItem: {
        paddingVertical: 15,
        paddingHorizontal: 20,
        borderBottomWidth: 0.5,
        borderBottomColor: '#eee',
    },
    selectedOption: {
        backgroundColor: ColorThemes.light.primary_main_color + '20',
    },
    optionText: {
        fontSize: 16,
        color: '#333',
    },
    selectedOptionText: {
        color: ColorThemes.light.primary_main_color,
        fontWeight: '500',
    },
});

export default CustomDropdown; 