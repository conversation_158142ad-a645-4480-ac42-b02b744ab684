import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { AppSvg, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import ConfigAPI from '../../Config/ConfigAPI';
import { GiftItem } from '../../modules/gift/giftDA';
import { Ultis } from '../../utils/Utils';
import iconSvg from '../../svg/icon';

interface GiftCardExchangeProps {
  item: GiftItem;
}

const GiftCardExchange: React.FC<GiftCardExchangeProps> = ({
  item
}) => {


  

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.8}>

      {/* Image */}
      <View style={styles.imageContainer}>
        <FastImage
          source={{ uri: ConfigAPI.urlImg + item.Img }}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.contentTop}>
          <Text style={styles.title} numberOfLines={2}>
            {item.Name}
          </Text>

          <Text style={styles.description} numberOfLines={1}>
          Số lượng: 1

          </Text>
        </View>

        {/* Points and Status */}
        <View style={styles.footer}>
          <View style={styles.pointsContainer}>
            <AppSvg SvgSrc={iconSvg.diamonIcon} size={16} />
            <Text style={styles.points}>{item.Value}</Text>
          </View>
          {item.Status === 0 ? (
            <Text style={[
              styles.statusText,
              styles.statusTextPending,              
            ]}>
              Chờ duyệt
            </Text>
          ) : item.Status === 1 ? (
            <Text style={[
              styles.statusText,
              styles.statusTextApproved,              
            ]}>
              Đã duyệt
            </Text>
          ) : (
            <Text style={[
              styles.statusText,
              styles.statusTextRejected,              
            ]}>
              Từ chối
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 12,
    marginVertical: 8,
    shadowColor: '#1890FF4D',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flexDirection: 'row',
    padding: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#F0F0F0',    
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  expiredBadge: {
    backgroundColor: '#FF4444',
  },
  outOfStockBadge: {
    backgroundColor: '#999999',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  contentTop: {
    flex: 1,
  },
  title: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '400',
    fontSize: 14,
  },
  description: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontWeight: '400',
    fontSize: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  points: {
    ...TypoSkin.body2,
    color: '#FEC015',
    fontWeight: '500',
    fontSize: 12,
  },
  statusContainer: {
    backgroundColor: '#FFA500',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  disabledStatusContainer: {
    backgroundColor: '#E0E0E0',
  },
  statusText: {
    ...TypoSkin.subtitle3,    
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFA500',
  },
  statusTextPending: {
    color: '#FFA500',
  },
  statusTextApproved: {
    color: '#2ECC71',
  },
  statusTextRejected: {
    color: '#E74C3C',
  },
  statusTextActive: {
    color: '#DA251D',
  },
  statusTextDisabled: {
    color: '#999999',
  },
});

export default GiftCardExchange;
