import React from 'react';
import { View } from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

export const GiftCardSkeleton = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 16,
        marginVertical: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        {/* Image */}
        <View style={{
          width: '100%',
          height: 200,
          borderRadius: 8,
          marginBottom: 12,
        }} />
        
        {/* Title */}
        <View style={{
          width: '80%',
          height: 18,
          borderRadius: 4,
          marginBottom: 8,
        }} />
        
        {/* Description */}
        <View style={{
          width: '100%',
          height: 14,
          borderRadius: 4,
          marginBottom: 4,
        }} />
        <View style={{
          width: '60%',
          height: 14,
          borderRadius: 4,
          marginBottom: 12,
        }} />
        
        {/* Points and Button */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <View style={{
            width: 80,
            height: 20,
            borderRadius: 4,
          }} />
          <View style={{
            width: 100,
            height: 36,
            borderRadius: 18,
          }} />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};

export const ExchangeHistoryCardSkeleton = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 16,
        marginVertical: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          {/* Image */}
          <View style={{
            width: 60,
            height: 60,
            borderRadius: 8,
            marginRight: 12,
          }} />
          
          {/* Content */}
          <View style={{ flex: 1 }}>
            {/* Title */}
            <View style={{
              width: '80%',
              height: 16,
              borderRadius: 4,
              marginBottom: 6,
            }} />
            
            {/* Date */}
            <View style={{
              width: '60%',
              height: 12,
              borderRadius: 4,
              marginBottom: 6,
            }} />
            
            {/* Points and Status */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
              <View style={{
                width: 60,
                height: 14,
                borderRadius: 4,
              }} />
              <View style={{
                width: 80,
                height: 24,
                borderRadius: 12,
              }} />
            </View>
          </View>
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};

export default { GiftCardSkeleton, ExchangeHistoryCardSkeleton };
