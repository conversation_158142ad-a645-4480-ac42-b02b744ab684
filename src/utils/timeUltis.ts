import dayjs from 'dayjs';

function checkTimeStatus(dateStart: number, dateEnd: number) {
  // L<PERSON>y thời gian hiện tại
  const now = dayjs();

  // Chuyển đổi timestamp thành dayjs object
  const startTime = dayjs(dateStart);
  const endTime = dayjs(dateEnd);

  // <PERSON><PERSON><PERSON> tra các trường hợp
  if (now.isBefore(startTime)) {
    return 'started';
  } else if (now.isAfter(startTime) && now.isBefore(endTime)) {
    return 'onGoing';
  } else if (now.isAfter(endTime)) {
    return 'ended';
  }
}

export {checkTimeStatus};
