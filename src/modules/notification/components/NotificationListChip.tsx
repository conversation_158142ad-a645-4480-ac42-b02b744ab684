import {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {Chip} from 'react-native-paper';

interface NotificationListChipProps {
  onChange: (type: 'general' | 'affiliate') => void;
}

const NotificationListChip = ({onChange}: NotificationListChipProps) => {
  const [choose, setChoose] = useState<'general' | 'affiliate'>('general');
  const isPrimary = (type: string) => {
    return choose === type ? styles.primaryChip : styles.secondaryChip;
  };
  const isPrimaryText = (type: string) => {
    return choose === type ? styles.primaryText : styles.secondaryText;
  };

  const handleChoose = (type: 'general' | 'affiliate') => {
    setChoose(type);
    onChange(type);
  };

  return (
    <View style={styles.containerChip}>
      <Chip
        mode="flat"
        style={[styles.chip, isPrimary('general')]}
        textStyle={isPrimaryText('general')}
        onPress={() => handleChoose('general')}>
        Thông báo chung
      </Chip>

      <Chip
        mode="flat"
        style={[styles.chip, isPrimary('affiliate')]}
        textStyle={isPrimaryText('affiliate')}
        onPress={() => handleChoose('affiliate')}>
        <View
          style={{
            flexDirection: 'row',
          }}>
          <Text style={isPrimaryText('affiliate')}>Affiliate</Text>
        </View>
      </Chip>
    </View>
  );
};

const styles = StyleSheet.create({
  containerChip: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingVertical: 12,
  },
  chip: {
    justifyContent: 'center',
    borderRadius: 20,
    height: 36,
  },
  primaryChip: {
    backgroundColor: '#1C33FF', // Blue background
  },
  primaryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  secondaryChip: {
    backgroundColor: '#E0E0E0', // Gray background
  },
  secondaryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationListChip;
