import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigateBack} from '../../router/router';
import {Ultis} from '../../utils/Utils';

import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useDispatch} from 'react-redux';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import RenderHTML from 'react-native-render-html';
import {getRankCustomer} from '../../redux/actions/customerAction';
import {FPopup, showPopup} from 'wini-mobile-components';

const {width} = Dimensions.get('window');

const ProfileRankScreen = () => {
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const [currentRank, setCurrentRank] = useState(0);
  const [ranksData, setRanksData] = useState<any[]>([]);

  const {rankInfo, rankInfoLoading} = useSelectorCustomerState();
  const popupRef = React.useRef<any>(null);

  useEffect(() => {
    if (customer?.Id) {
      dispatch(getRankCustomer({Id: customer.Id}));
    }
  }, [customer?.Id]);

  useEffect(() => {
    if (customer && rankInfo?.totalReward && rankInfoLoading === false) {
      const currentRank = rankInfo?.totalReward || 0;
      const ranksData = rankInfo?.RanksData || [];
      setCurrentRank(currentRank);
      setRanksData(ranksData);
    }
  }, [customer, rankInfo, rankInfoLoading]);

  // Tính toán progress đến hạng tiếp theo dựa trên currentRank
  const getCustomerRankProgress = () => {
    if (ranksData.length === 0) {
      return {
        progress: 0,
        nextPoints: 0,
        currentRankInfo: null,
        nextRankInfo: null,
      };
    }

    // Sắp xếp ranks theo điểm số tăng dần
    const sortedRanks = [...ranksData].sort(
      (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
    );

    // Tìm hạng hiện tại và hạng tiếp theo
    let currentRankInfo = null;
    let nextRankInfo = null;

    for (let i = 0; i < sortedRanks.length; i++) {
      const rankScore = parseFloat(sortedRanks[i].Score);
      if (currentRank >= rankScore) {
        currentRankInfo = sortedRanks[i];
        // kiểm tra nếu đã đạt hạng cao nhất thiết lập nextRankInfo = bằng currentRankInfo
        if (i === sortedRanks.length - 1) {
          nextRankInfo = currentRankInfo;
        } else {
          nextRankInfo = sortedRanks[i + 1];
        }

        // nextRankInfo = sortedRanks[i + 1] || null;
      } else {
        if (!currentRankInfo) {
          nextRankInfo = sortedRanks[i];
        }
        break;
      }
    }

    // Tính toán progress
    let progress = 0;
    if (nextRankInfo && currentRankInfo.Score !== nextRankInfo.Score) {
      const currentRankPoints = currentRankInfo
        ? parseFloat(currentRankInfo.Score)
        : 0;
      const nextRankPoints = parseFloat(nextRankInfo.Score);
      const totalNeeded = nextRankPoints - currentRankPoints;
      const currentProgress = currentRank;

      progress = totalNeeded > 0 ? (currentProgress / totalNeeded) * 100 : 0;
    } else {
      // Đã đạt hạng cao nhất
      progress = 100;
    }

    return {
      progress: Math.max(0, Math.min(100, progress)),
      nextPoints: nextRankInfo ? parseFloat(nextRankInfo.Score) : 0,
      currentRankInfo,
      nextRankInfo,
    };
  };
  const {progress, nextPoints, currentRankInfo, nextRankInfo} =
    getCustomerRankProgress();

  return (
    <View style={styles.safeArea}>
      <FPopup ref={popupRef} />
      {/* Header */}
      <InforHeader title="Điểm của bạn" onBack={navigateBack} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Status Card */}
        <View style={styles.statusCard}>
          {currentRankInfo?.Icon ? (
            <FastImage
              source={{
                uri: ConfigAPI.urlImg + currentRankInfo?.Icon,
              }}
              style={{width: 60, height: 60}}
              resizeMode="contain"
            />
          ) : null}
          <Text style={styles.statusTitle}>
            {currentRankInfo?.Name
              ? currentRankInfo?.Name
              : 'Chưa được xếp hạng'}
          </Text>
        </View>
        {/* Progress Section */}
        <View style={styles.progressSection}>
          {/* Current Rank Progress */}
          <View style={styles.currentRankProgress}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Tiến độ hạng hiện tại</Text>
              <Text style={styles.progressPercentage}>
                {Math.round(progress)}%
              </Text>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <View
                  style={[styles.progressBarFill, {width: `${progress}%`}]}
                />
              </View>
            </View>

            {/* Progress Info */}
            <View style={styles.progressInfo}>
              <View style={styles.progressInfoItem}>
                <Text style={styles.progressInfoLabel}>Hạng hiện tại</Text>
                <Text style={styles.progressInfoValue}>
                  {currentRankInfo?.Name || 'Chưa có hạng'}
                </Text>
              </View>
              {nextRankInfo && (
                <View style={styles.progressInfoItem}>
                  <Text style={styles.progressInfoLabel}>Hạng tiếp theo</Text>
                  <Text style={styles.progressInfoValue}>
                    {nextRankInfo?.Name}
                  </Text>
                </View>
              )}
            </View>

            {/* Current Points Display */}
            <View style={styles.currentPointsDisplay}>
              <Text style={styles.currentPointsLabel}>Điểm hiện tại</Text>
              <Text style={styles.currentPointsValue}>
                {Ultis.money(currentRank || 0)} điểm
              </Text>
              {nextRankInfo && (
                <Text style={styles.pointsToNext}>
                  {currentRankInfo?.Id == nextRankInfo?.Id
                    ? 'Đã đạt hạng cao nhất'
                    : `Cần ${Ultis.money(
                        nextPoints - (currentRank || 0),
                      )} điểm để lên hạng`}
                </Text>
              )}
            </View>
          </View>
        </View>
        {/* mô tả điều kiện */}
        {nextRankInfo?.Condition && (
          <View style={styles.rankingSection}>
            <Text style={styles.sectionTitle}>Mô tả</Text>
            <View style={styles.rankingGrid}>
              <RenderHTML
                contentWidth={width}
                source={{html: nextRankInfo?.Condition}}
              />
            </View>
          </View>
        )}

        {/* Ranking Tiers Section */}
        <View style={styles.rankingSection}>
          <Text style={styles.sectionTitle}>Chỉ tiêu hạng</Text>
          <View style={styles.rankingGrid}>
            {ranksData?.map((tier: any) => (
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  showPopup({
                    ref: popupRef,
                    enableDismiss: true,
                    children: (
                      <View
                        style={{
                          padding: 16,
                          height: Dimensions.get('window').height / 2,
                          borderRadius: 16,
                          width: '100%',
                        }}>
                        <View style={styles.rankingSection}>
                          <Text style={styles.sectionTitle}>Mô tả</Text>
                          <View style={styles.rankingGrid}>
                            <RenderHTML
                              contentWidth={width}
                              source={{html: tier?.Condition}}
                            />
                          </View>
                        </View>
                      </View>
                    ),
                  });
                }}
                key={tier.Id}
                style={styles.rankTierCard}>
                <View style={[styles.rankTierIcon]}>
                  <FastImage
                    key={tier.Icon}
                    source={{
                      uri: ConfigAPI.urlImg + tier.Icon,
                    }}
                    style={{width: 30, height: 30}}
                  />
                </View>
                <Text style={styles.rankTierLabel}>Hạng {tier.Name}</Text>
                <Text style={styles.rankTierPoints}>
                  {Ultis.money(parseFloat(tier.Score))} điểm
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 16,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 16,
    top: 12,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.title,
  },
  menuButton: {
    position: 'absolute',
    right: 16,
    top: 12,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  scrollView: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  trophySection: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  trophyContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    width: width - 32,
  },
  decorativeElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },

  trophy: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 50,
    padding: 20,
  },
  statusCard: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 16,
    marginHorizontal: 16,
    padding: 20,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    gap: 16,
  },
  statusTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
  statusSubtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  progressSection: {
    backgroundColor: ColorThemes.light.primary_background,
    margin: 16,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentRankProgress: {
    marginBottom: 0,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  progressPercentage: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.primary_main_color,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  progressInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  progressInfoLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  progressInfoValue: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  currentPointsDisplay: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  currentPointsLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  pointsToNext: {
    ...TypoSkin.body3,
    color: ColorThemes.light.primary_main_color,
    textAlign: 'center',
  },
  pointsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  pointsInfo: {
    flex: 1,
  },
  pointsLabel: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 2,
  },
  pointsSubLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  pointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  progressIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressItem: {
    alignItems: 'center',
    flex: 1,
  },
  progressIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  rankingSection: {
    margin: 16,
    marginTop: 0,
    borderRadius: 20,
    padding: 16,
    backgroundColor: ColorThemes.light.primary_background,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
  },
  rankingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  rankTierCard: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  rankTierIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  rankTierLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
    textAlign: 'center',
  },
  rankTierPoints: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  achievementsSection: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
    marginTop: 0,
    borderRadius: 16,
    padding: 16,

    marginBottom: 32,
  },
  achievementCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementName: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 2,
  },
  achievementDescription: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  achievementProgress: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  achievementProgressText: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
});

export default ProfileRankScreen;
