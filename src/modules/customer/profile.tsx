import {
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigate, RootScreen} from '../../router/router';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {Ultis} from '../../utils/Utils';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import MenuHaveShop from '../../components/Field/menu/MenuHaveShop/MenuHaveShop';
import iconSvg from '../../svg/icon';
import {StatusOrder, Title} from '../../Config/Contanst';
import MenuOrders from './setting/menuOrders';
import {useEffect, useState} from 'react';
import {OrderActions} from '../../redux/reducers/OrderReducer';
import {DataController} from '../../base/baseController';

const actionList = [
  {
    id: 0,
    name: 'Tài khoản',
    icon: 'fill/users/profile',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.SettingProfile,
  },

  {
    id: 3,
    name: 'Thiết lập sinh trắc học',
    icon: 'fill/technology/face-recognition',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.BiometricSetting,
  },
  {
    id: 4,
    name: 'Xác thực 2 lớp',
    icon: 'fill/technology/lock-portrait',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.TwoFactorAuth,
  },
  {
    id: 16,
    name: 'Xác minh tài khoản',
    icon: 'fill/user interface/verified',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.AccountAuth,
  },
  {
    id: 6,
    name: 'Sản phẩm yêu thích',
    icon: 'outline/user interface/favorite',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.FavoriteProduct,
  },
  {
    id: 8,
    name: 'Thông tin nhận hàng',
    icon: 'fill/location/map-marker',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.MyAddress,
  },
  {
    id: 7,
    name: 'FAQ',
    show: true,
    icon: 'fill/layout/circle-question',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.FAQView,
  },
  {
    id: 1,
    name: 'Chính sách',
    show: true,
    icon: 'fill/shopping/list',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.PolicyView,
  },
  {
    id: 9,
    name: 'Đăng xuất',
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    background: ColorThemes.light.neutral_main_background_color,
    colorIcon: ColorThemes.light.neutral_text_title_color,
    route: RootScreen.login,
  },
];

export default function Profile({select}: any) {
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const currentRank = customer?.TotalReward || 0;
  const ranksData = customer?.RanksData || [];
  const orderController = new DataController('Order');
  const [orderDetail, setOrderDetail] = useState<any>([]);
  const navigation = useNavigation<any>();

  //navigation

  useEffect(() => {
    console.log(select);
    if (select == 'Cá nhân') {
      // call order
      getOrderDetail();
    }
  }, [select]);

  useEffect(() => {
    //focus effect
    const unsubscribe = navigation.addListener('focus', async () => {
      getOrderDetail();
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (customer) dispatch(CustomerActions.getTotalReward(customer));
  }, []);
  const getOrderDetail = async () => {
    const response = await orderController.getPatternList({
      query: `@CustomerId: {${customer?.Id}}`,
      pattern: {
        ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
      },
    });
    if (response?.code === 200) {
      setOrderDetail(response.data);
      return response;
    }
  };
  // Tính toán progress đến hạng tiếp theo dựa trên currentRank
  const getCustomerRankProgress = () => {
    if (ranksData.length === 0) {
      return {
        progress: 0,
        nextPoints: 0,
        currentRankInfo: null,
        nextRankInfo: null,
      };
    }

    // Sắp xếp ranks theo điểm số tăng dần
    const sortedRanks = [...ranksData].sort(
      (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
    );

    // Tìm hạng hiện tại và hạng tiếp theo
    let currentRankInfo = null;
    let nextRankInfo = null;

    for (let i = 0; i < sortedRanks.length; i++) {
      const rankScore = parseFloat(sortedRanks[i].Score);
      if (currentRank >= rankScore) {
        currentRankInfo = sortedRanks[i];
        // kiểm tra nếu đã đạt hạng cao nhất thiết lập nextRankInfo = bằng currentRankInfo
        if (i === sortedRanks.length - 1) {
          nextRankInfo = currentRankInfo;
        } else {
          nextRankInfo = sortedRanks[i + 1];
        }

        // nextRankInfo = sortedRanks[i + 1] || null;
      } else {
        if (!currentRankInfo) {
          nextRankInfo = sortedRanks[i];
        }
        break;
      }
    }

    // Tính toán progress
    let progress = 0;
    if (nextRankInfo) {
      const currentRankPoints = currentRankInfo
        ? parseFloat(currentRankInfo.Score)
        : 0;
      const nextRankPoints = parseFloat(nextRankInfo.Score);
      const totalNeeded = nextRankPoints - currentRankPoints;
      const currentProgress = currentRank - currentRankPoints;
      progress = totalNeeded > 0 ? (currentProgress / totalNeeded) * 100 : 0;
    } else {
      // Đã đạt hạng cao nhất
      progress = 100;
    }

    return {
      progress: Math.max(0, Math.min(100, progress)),
      nextPoints: nextRankInfo ? parseFloat(nextRankInfo.Score) : 0,
      currentRankInfo,
      nextRankInfo,
    };
  };
  const {progress, nextPoints, currentRankInfo, nextRankInfo} =
    getCustomerRankProgress();
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      {customer ? (
        <TouchableOpacity
          onPress={() => {
            navigate(RootScreen.ProfileRankScreen);
          }}
          style={styles.progressSection}>
          <View
            style={{
              ...(currentRankInfo?.Id == nextRankInfo?.Id && {
                justifyContent: 'center',
                alignContent: 'center',
                alignItems: 'center',
              }),
            }}>
            {currentRankInfo?.Id == nextRankInfo?.Id && (
              <FastImage
                source={{
                  uri: ConfigAPI.urlImg + currentRankInfo?.Icon,
                }}
                style={{width: 45, height: 45}}
                resizeMode="contain"
              />
            )}
            <Text style={styles.progressInfoValue}>
              {currentRankInfo?.Name || 'Chưa có hạng'}
            </Text>
            {nextRankInfo ? (
              <Text style={styles.pointsToNext}>
                {currentRankInfo?.Id === nextRankInfo?.Id
                  ? 'Đã đạt hạng cao nhất'
                  : `Cần ${Ultis.money(
                      nextPoints - (currentRank || 0),
                    )} điểm để lên hạng`}
              </Text>
            ) : null}
          </View>

          {/* progress */}
          {currentRankInfo?.Id === nextRankInfo?.Id ? null : (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 4,
              }}>
              {currentRankInfo?.Icon ? (
                <FastImage
                  source={{
                    uri: ConfigAPI.urlImg + currentRankInfo?.Icon,
                  }}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              ) : (
                <FastImage
                  source={require('../../assets/images/logo.png')}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              )}
              {/* progress */}
              <View style={styles.progressBarBackground}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.secondary1_main_color,
                  }}>
                  {Ultis.money(currentRank || 0)}/{Ultis.money(nextPoints)}
                </Text>
                <View
                  style={[styles.progressBarFill, {width: `${progress}%`}]}
                />
              </View>
              {nextRankInfo?.Icon ? (
                <FastImage
                  source={{
                    uri: ConfigAPI.urlImg + nextRankInfo?.Icon,
                  }}
                  style={{width: 30, height: 30}}
                  resizeMode="contain"
                />
              ) : null}
            </View>
          )}
        </TouchableOpacity>
      ) : null}
      {customer ? (
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around',
            paddingHorizontal: 8,
            marginBottom: 8,
          }}>
          <MenuOrders
            svgIcon={iconSvg.walletAction}
            title="Đơn hàng mới"
            getBadgeOrder={
              orderDetail?.filter((item: any) => item.Status == StatusOrder.new)
                .length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.new}
          />
          <MenuOrders
            svgIcon={iconSvg.deliveryIcon}
            title="Đang xử lý"
            getBadgeOrder={
              orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.proccess,
              ).length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.proccess}
          />
          <MenuOrders
            svgIcon={iconSvg.done}
            title="Hoàn thành"
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.success}
          />
          <MenuOrders
            svgIcon={iconSvg.cancel}
            title="Hủy/hoàn"
            getBadgeOrder={
              orderDetail?.filter(
                (item: any) => item.Status == StatusOrder.cancel,
              ).length
            }
            orderRoute={RootScreen.OrderCustomerDetail}
            status={StatusOrder.cancel}
          />
          <MenuOrders
            svgIcon={iconSvg.star}
            title="Đánh giá"
            orderRoute={RootScreen.Review}
          />
        </View>
      ) : null}
      {(!customer ? actionList.filter(item => item.show) : actionList).map(
        item => (
          <ListTile
            key={item.id}
            style={{
              padding: 0,
            }}
            listtileStyle={{
              paddingRight: 16,
              paddingVertical: 13,
              gap: 8,
              borderBottomColor: ColorThemes.light.primary_background,
              borderBottomWidth: 1,
              marginLeft: 16,
            }}
            onPress={() => {
              if (item.action === 'logout') {
                // Handle logout action here
                console.log('Logout action triggered');
                // navigateReset(RootScreen.login);
                dispatch(CustomerActions.logout());
                return;
              }

              if (item.route) navigate(item.route);
            }}
            leading={
              <View
                style={{
                  height: 32,
                  width: 32,
                  borderRadius: 4,
                  padding: 6,
                  backgroundColor: item.background,
                }}>
                <Winicon src={item.icon} color={item.colorIcon} size={20} />
              </View>
            }
            title={
              !customer && item.action === 'logout' ? 'Về đăng nhập' : item.name
            }
            titleStyle={[
              TypoSkin.heading8,
              {color: ColorThemes.light.neutral_text_title_color},
            ]}
            trailing={
              <Winicon
                src="outline/arrows/right-arrow"
                color={ColorThemes.light.neutral_text_subtitle_color}
                size={16}
              />
            }
          />
        ),
      )}
    </View>
  );
}
const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  progressSection: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
    borderRadius: 20,
    padding: 16,
    gap: 8,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
  },
  currentRankProgress: {
    marginBottom: 0,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  progressPercentage: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.primary_main_color,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 24,
    backgroundColor: ColorThemes.light.secondary1_background,
    borderRadius: 50,
    overflow: 'hidden',
    flex: 1,
    alignItems: 'center',
    alignContent: 'center',
    alignSelf: 'center',
    textAlign: 'center',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  progressInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  progressInfoLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  progressInfoValue: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  currentPointsDisplay: {
    backgroundColor: ColorThemes.light.secondary3_main_color,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  currentPointsLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  pointsToNext: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});
