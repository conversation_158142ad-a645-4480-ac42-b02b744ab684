import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';

/**
 * Component demo để test các màu avatar kh<PERSON>c nhau
 * Sử dụng để kiểm tra random color generation
 */
const AvatarColorDemo: React.FC = () => {
  // Same function as in ChatRoomScreen
  const getRandomAvatarColor = (name: string) => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const testNames = [
    'An Nguyễn',
    '<PERSON><PERSON><PERSON> Trần', 
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON> V<PERSON>',
    '<PERSON> Đặng',
    'Oanh Tô',
    'Phúc Ngô',
  ];

  const renderAvatar = (name: string, size: number = 60) => {
    const avatarColor = getRandomAvatarColor(name);
    const fontSize = size * 0.4; // 40% of avatar size
    
    return (
      <View style={styles.avatarItem}>
        <View 
          style={[
            styles.avatar, 
            { 
              width: size, 
              height: size, 
              borderRadius: size / 2,
              backgroundColor: avatarColor 
            }
          ]}
        >
          <Text style={[styles.avatarText, { fontSize }]}>
            {name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <Text style={styles.nameText} numberOfLines={1}>
          {name}
        </Text>
        <Text style={styles.colorText}>
          {avatarColor}
        </Text>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>Avatar Color Demo</Text>
      <Text style={styles.subtitle}>
        Test random colors cho avatar fallback
      </Text>

      {/* Large avatars */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Large Avatars (120px)</Text>
        <View style={styles.avatarGrid}>
          {testNames.slice(0, 6).map((name) => (
            <View key={name} style={styles.largeAvatarContainer}>
              {renderAvatar(name, 120)}
            </View>
          ))}
        </View>
      </View>

      {/* Medium avatars */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Medium Avatars (80px)</Text>
        <View style={styles.avatarGrid}>
          {testNames.slice(6, 12).map((name) => (
            <View key={name} style={styles.mediumAvatarContainer}>
              {renderAvatar(name, 80)}
            </View>
          ))}
        </View>
      </View>

      {/* Small avatars */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Small Avatars (50px)</Text>
        <View style={styles.avatarGrid}>
          {testNames.slice(12).map((name) => (
            <View key={name} style={styles.smallAvatarContainer}>
              {renderAvatar(name, 50)}
            </View>
          ))}
        </View>
      </View>

      {/* Color palette */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Color Palette</Text>
        <View style={styles.colorPalette}>
          {[
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
          ].map((color, index) => (
            <View key={color} style={styles.colorItem}>
              <View style={[styles.colorSwatch, { backgroundColor: color }]} />
              <Text style={styles.colorLabel}>
                {index}: {color}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Test consistency */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Consistency Test</Text>
        <Text style={styles.description}>
          Cùng một tên phải có cùng màu mỗi lần render
        </Text>
        <View style={styles.consistencyTest}>
          {['An Nguyễn', 'Bình Trần', 'Cường Lê'].map((name) => (
            <View key={name} style={styles.consistencyRow}>
              <Text style={styles.consistencyName}>{name}:</Text>
              {[1, 2, 3].map((i) => (
                <View key={i} style={styles.consistencyAvatar}>
                  {renderAvatar(name, 40)}
                </View>
              ))}
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_color,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginBottom: 12,
  },
  avatarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  avatarItem: {
    alignItems: 'center',
    marginBottom: 16,
  },
  largeAvatarContainer: {
    width: '45%',
    marginBottom: 20,
  },
  mediumAvatarContainer: {
    width: '30%',
    marginBottom: 16,
  },
  smallAvatarContainer: {
    width: '25%',
    marginBottom: 12,
  },
  avatar: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatarText: {
    fontWeight: 'bold',
    color: 'white',
  },
  nameText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_color,
    textAlign: 'center',
    marginBottom: 4,
  },
  colorText: {
    fontSize: 10,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    fontFamily: 'monospace',
  },
  colorPalette: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorItem: {
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  colorSwatch: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 8,
  },
  colorLabel: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_color,
    fontFamily: 'monospace',
  },
  consistencyTest: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 16,
    borderRadius: 8,
  },
  consistencyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  consistencyName: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_color,
    width: 80,
  },
  consistencyAvatar: {
    marginLeft: 8,
  },
});

export default AvatarColorDemo;
