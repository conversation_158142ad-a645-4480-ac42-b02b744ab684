import { BaseDA } from '../../../base/BaseDA';
import { DataController } from '../../../base/baseController';
import ConfigAPI from '../../../Config/ConfigAPI';
import { ChatRoom, ChatMessage, CreateGroupRequest } from '../types/ChatTypes';
import { getMockChatRooms, getMockMessages, getMockUsers } from '../../../mock/chatData';
import { randomGID } from '../../../utils/Utils';

interface Customer {
  Id: string;
  Name: string;
  Avatar?: string;
  Mobile?: string;
  Email?: string;
  IsOnline?: boolean;
}

class ChatAPI {
  private chatController: DataController;
  private messageController: DataController;
  private useMockData: boolean = true; // Set to false when real API is ready

  constructor() {
    this.chatController = new DataController('ChatRoom');
    this.messageController = new DataController('Message');
  }

  // L<PERSON>y danh sách phòng chat
  async getChatRooms(page: number = 1, size: number = 20): Promise<{ data: ChatRoom[]; total: number }> {
    try {
      const response = await this.chatController.aggregateList({
        page,
        size,
        sortby: [{ prop: 'updatedAt', direction: 'DESC' }],
      });
      return {
        data: response.data || [],
        total: response.total || 0,
      };
    } catch (error) {
      console.error('Error fetching chat rooms:', error);
      throw error;
    }
  }

  // Lấy tin nhắn trong phòng chat
  async getMessages(roomId: string, page: number = 1, size: number = 50): Promise<{ data: ChatMessage[]; total: number }> {
    try {
      const response = await this.messageController.getPatternList({
        page,
        size,
        query: `@ChatRoomId:{${roomId}}`,
        returns: ['Id', 'Content', 'DateCreated', 'Type', 'FileUrl', 'ChatRoomId', 'CustomerId'],
        pattern: {
          CustomerId: ['Id', 'Name', 'Avatar'],
        },
      });
      if(response.code === 200) {
        response.data = response.data.map((item: any) => {
          return {
            ...item,
            user: response.Customer?.find((customer: any) => customer.Id == item.CustomerId),
          };
        });
        return {
          data: response.data || [],
          total: response.total || 0,
        };
      }
      return {
        data: [],
        total: 0,
      };     
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  // Tạo phòng chat 1-1
  async createPrivateRoom(participantId: string): Promise<ChatRoom> {
    try {
      const response = await BaseDA.post(ConfigAPI.url + 'chat/create-private-room', {
        body: { participantId },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating private room:', error);
      throw error;
    }
  }

  // Tạo nhóm chat
  async createGroupRoom(groupData: CreateGroupRequest): Promise<ChatRoom> {
    try {
      const response = await BaseDA.post(ConfigAPI.url + 'chat/create-group-room', {
        body: groupData,
      });
      return response.data;
    } catch (error) {
      console.error('Error creating group room:', error);
      throw error;
    }
  }

  // Gửi tin nhắn
  async sendMessage(messageData: ChatMessage){
    try {
      const response = await this.messageController.add([messageData]);
      if(response.code === 200) {
        return true;
      }
      return false;
     
    } catch (error) { 
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Upload file/image cho chat
  async uploadChatFile(file: any, type: 'image' | 'video' | 'audio' | 'file'): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await BaseDA.post(ConfigAPI.url + 'chat/upload-file', {
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.url;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  // Tìm kiếm người dùng để thêm vào chat
  async searchUsers(query: string): Promise<any[]> {
    if (this.useMockData) {
      return getMockUsers(query);
    }

    try {
      const userController = new DataController('Customer');
      const response = await userController.aggregateList({
        searchRaw: query,
        size: 20,
      });
      return response.data || [];
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Đánh dấu tin nhắn đã đọc
  async markAsRead(roomId: string): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/mark-as-read', {
        body: { roomId },
      });
    } catch (error) {
      console.error('Error marking as read:', error);
      throw error;
    }
  }

  // Rời khỏi nhóm chat
  async leaveGroup(roomId: string): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/leave-group', {
        body: { roomId },
      });
    } catch (error) {
      console.error('Error leaving group:', error);
      throw error;
    }
  }

  // Thêm thành viên vào nhóm
  async addMembersToGroup(roomId: string, memberIds: string[]): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/add-members', {
        body: { roomId, memberIds },
      });
    } catch (error) {
      console.error('Error adding members:', error);
      throw error;
    }
  }

  // Lấy danh sách tất cả Customer
  async getAllCustomers(): Promise<Customer[]> {
    try {
      const customerController = new DataController('Customer');
      const response = await customerController.aggregateList({
        page: 1,
        size: 1000, // Lấy nhiều để có đủ danh bạ
        sortby: [{ prop: 'Name', direction: 'ASC' }],
      });
      return response.data || [];
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  // Tìm hoặc tạo ChatRoom cho chat 1-1
  async findOrCreatePrivateRoom(currentUserId: string, Contact: any): Promise<ChatRoom> {
    try {
      // Tạo CustomerId string để tìm kiếm (sort để đảm bảo thứ tự nhất quán)
      const customerIds = [currentUserId, Contact.Id].sort().join(',');

      // Tìm ChatRoom đã tồn tại
      const response = await this.chatController.getListSimple({
        page: 1,
        size: 1,
        query: `@Members:(${customerIds}) -@IsGroup:{true}`,
      });

      if (response.data && response.data.length > 0) {
        // Đã có ChatRoom, trả về
        return response.data[0];
      }

      const newRoom: Partial<ChatRoom> = {
        Name: Contact.Name,
        Avatar: Contact.Avatar,
        CustomerId: currentUserId,
        Members: customerIds,
        IsGroup: false,
        LastMessage: null,
        UpdatedAt: new Date().getTime(),
        DateCreated: new Date().getTime(),
        Id: randomGID(),
      };

      const createResponse = await this.chatController.add([newRoom]);
      if (createResponse.code === 200 && createResponse.data.length > 0) {
        return createResponse.data[0];
      }

      throw new Error('Failed to create chat room');
    } catch (error) {
      console.error('Error finding or creating private room:', error);
      throw error;
    }
  }

  // Lấy thông tin Customer theo ID
  async getCustomerById(customerId: string): Promise<Customer | null> {
    try {
      const customerController = new DataController('Customer');
      const response = await customerController.getById(customerId);

      if (response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
      throw error;
    }
  }

  // Lấy ChatRooms của user hiện tại
  async getChatRoomsForUser(userId: string, page: number = 1, size: number = 20): Promise<{ data: ChatRoom[]; total: number }> {
    
    try {
      // Tìm các ChatRoom mà user tham gia
      const response = await this.chatController.aggregateList({
        page,
        size,
        searchRaw: `@CustomerId:{${userId}}`, // Tìm trong CustomerId string
        sortby: [{ prop: 'DateCreated', direction: 'DESC' }],
      });

      return {
        data: response.data || [],
        total: response.total || 0,
      };
    } catch (error) {
      console.error('Error fetching user chat rooms:', error);
      throw error;
    }
  }
}

export default new ChatAPI();
