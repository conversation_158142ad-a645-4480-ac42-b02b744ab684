import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Image,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import { ColorThemes } from '../../../assets/skin/colors';
import { useChatRooms, useChatLoading, useChatConnectionStatus } from '../../../redux/hook/chatHook';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import { fetchChatRoomsForUser, connectSocket } from '../../../redux/reducers/ChatReducer';
import { ChatRoom } from '../types/ChatTypes';
import { navigate, RootScreen } from '../../../router/router';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import { getDataToAsyncStorage } from '../../../utils/AsyncStorage';
import { StorageContanst } from '../../../Config/Contanst';
import { Ultis } from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';

const ChatListScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const chatRooms = useChatRooms();
  const loading = useChatLoading();
  const isConnected = useChatConnectionStatus();
  const customer = useSelectorCustomerState().data;
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (customer) {
        initializeChat();
      }
    }, [customer])
  );

  const initializeChat = async () => {
    try {
      // Kết nối socket nếu chưa kết nối
      if (!isConnected && customer) {
        const token = await getDataToAsyncStorage(StorageContanst.accessToken);
        if (token) {
          dispatch(connectSocket(customer.id));
        }
      }
      
      // Tải danh sách chat rooms cho user hiện tại
      const userId = customer.Id || customer.id;
      if (userId) {
        dispatch(fetchChatRoomsForUser(userId, 1));
      }
    } catch (error) {
      console.error('Error initializing chat:', error);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      const userId = customer?.Id || customer?.id;
      if (userId) {
        await dispatch(fetchChatRoomsForUser(userId, 1));
      }
    } catch (error) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải danh sách chat',
      });
    } finally {
      setRefreshing(false);
    }
  }, [dispatch, customer]);

  const navigateToChatRoom = (room: ChatRoom) => {
    navigate(RootScreen.ChatRoom, { room });
  };

  const navigateToCreateGroup = () => {
    navigate(RootScreen.CreateGroup);
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const messageDate = new Date(date);
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffInHours < 24 * 7) {
      return messageDate.toLocaleDateString('vi-VN', { weekday: 'short' });
    } else {
      return messageDate.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  };

  const renderChatItem = ({ item }: { item: ChatRoom }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => navigateToChatRoom(item)}
      activeOpacity={0.7}
    >
      <View style={styles.avatarContainer}>
        {item.Avatar ? (
          <FastImage source={{ uri: ConfigAPI.urlImg + item.Avatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.defaultAvatar]}>
            <Text style={styles.avatarText}>
              {item.Name?.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        {item.IsGroup && (
          <View style={styles.groupIndicator}>
            <Text style={styles.groupIndicatorText}>G</Text>
          </View>
        )}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatName} numberOfLines={1}>
            {item.Name}
          </Text>
          {item.UpdatedAt && (
            <Text style={styles.timeText}>
              {Ultis.formatDateTime(item.UpdatedAt, false)}
            </Text>
          )}
        </View>

        <View style={styles.chatFooter}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.LastMessage?.Content || ''}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Chưa có cuộc trò chuyện nào</Text>
      <TouchableOpacity style={styles.createButton} onPress={navigateToCreateGroup}>
        <Text style={styles.createButtonText}>Tạo nhóm chat</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={chatRooms}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.Id || item.id || ''}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={chatRooms.length === 0 ? styles.emptyList : undefined}
      />

      {!isConnected && (
        <View style={styles.connectionStatus}>
          <Text style={styles.connectionText}>Đang kết nối...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },

  chatItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  groupIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.secondary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupIndicatorText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    flex: 1,
  },
  timeText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  chatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: ColorThemes.light.error_color,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    marginBottom: 16,
  },
  createButton: {
    backgroundColor: ColorThemes.light.primary_color,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  connectionStatus: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    backgroundColor: ColorThemes.light.warning_color,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  connectionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default ChatListScreen;
