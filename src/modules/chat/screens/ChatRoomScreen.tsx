import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Platform,
  SafeAreaView,
  StatusBar,
  Image,
} from 'react-native';
import {
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal,
  Dimensions,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useRoute, useNavigation } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
// import DocumentPicker from 'react-native-document-picker'; // Removed due to compatibility issues
import { ColorThemes } from '../../../assets/skin/colors';
import { useChatMessages, useSelectorChatState } from '../../../redux/hook/chatHook';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import {
  fetchMessages,
  addMessage,
  setCurrentRoom,
} from '../../../redux/reducers/ChatReducer';
import { ChatRoom, ChatMessage } from '../types/ChatTypes';
import SocketService from '../services/SocketService';
import ChatAPI from '../services/ChatAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import EmojiPicker from '../components/EmojiPicker';
import { PermissionHelper } from '../utils/PermissionHelper';
import { MessageConverter } from '../utils/MessageConverter';
import FastImage from 'react-native-fast-image';
import { InforHeader } from '../../../Screen/Layout/headers/inforHeader';
import ConfigAPI from '../../../Config/ConfigAPI';
import { randomGID } from '../../../utils/Utils';
import { BaseDA } from '../../../base/BaseDA';

interface RouteParams {
  room: ChatRoom;
}

const ChatRoomScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const { room } = route.params as RouteParams;



  const customer = useSelectorCustomerState().data;
  const roomId = room.Id || room.id || '';
  const messages = useChatMessages(roomId);
  const chatState = useSelectorChatState();

  const [isTyping, setIsTyping] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputText, setInputText] = useState('');
  const [showToolbar, setShowToolbar] = useState(false); // Mặc định ẩn toolbar mở rộng
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    if (room && roomId) {
      dispatch(setCurrentRoom(room));
      dispatch(fetchMessages(roomId, 1));
      // dispatch(resetUnreadCount(roomId));

      // Join room for socket
      SocketService.joinRoom(roomId);

      // Setup socket listeners
      SocketService.onReceiveMessage((data) => {
        const { roomId: messageRoomId, fromUserId, message } = data;
        if (messageRoomId === roomId) {
          // Ensure message is in proper database format
          const chatMessage: ChatMessage = {
            Id: message.Id || generateUniqueId(),
            Content: message.Content || message.text || message,
            DateCreated: message.DateCreated || new Date().getTime(),
            Type: message.Type || '1', // Default to text
            FileUrl: message.FileUrl,
            CustomerId: fromUserId,
            ChatRoomId: messageRoomId,
            user: {
              Id: fromUserId,
              Name: 'User', // TODO: Get user name from contacts
            },
            received: true,
          };
          dispatch(addMessage({ roomId: messageRoomId, message: chatMessage }));
        }
      });

      // Setup typing listener
      SocketService.onUserTyping((data) => {
        const { roomId: typingRoomId, fromUserId } = data;
        if (typingRoomId === roomId && fromUserId !== (customer?.Id || customer?.id)) {
          setIsTyping(true);
          // Clear typing after 3 seconds
          setTimeout(() => setIsTyping(false), 3000);
        }
      });
    }

    return () => {
      if (roomId) {
        SocketService.leaveRoom(roomId);
      }
      dispatch(setCurrentRoom(null));
    };
  }, [roomId, dispatch, customer]);

  // Prepare messages for display (newest at bottom, oldest at top)
  const displayMessages = [...messages].sort((a, b) => a.DateCreated - b.DateCreated);

  // Generate unique message ID
  const generateUniqueId = () => {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  };

  // Generate random background color for avatar
  const getRandomAvatarColor = (name: string) => {

    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };


  // Handle sending message
  const handleSendMessage = useCallback(async (text: string, type: number = 1, fileUrl?: string) => {
    if (!text.trim() && !fileUrl) return;

    try {
      // Clear input text immediately
      setInputText('');

      // Create message in database format
      const chatMessage: ChatMessage = {
        Id: randomGID(),
        Content: text,
        DateCreated: new Date().getTime(),
        Type: type,
        FileUrl: fileUrl,
        CustomerId: customer?.Id || customer?.id || '',
        ChatRoomId: roomId,
        user: {
          Id: customer?.Id,
          Name: customer?.Name,
          Avatar: customer?.AvatarUrl,
        },
        sent: true,
        received: false,
      };
      console.log('Adding message to store:', chatMessage);
      dispatch(addMessage({ roomId, message: chatMessage }));

      // Send via socket
      SocketService.sendMessage(roomId, chatMessage);

      // Also send via API for persistence
      await ChatAPI.sendMessage({
        Id: randomGID(),
        Content: text,
        DateCreated: new Date().getTime(),
        Type: type,
        FileUrl: fileUrl,
        CustomerId: customer?.Id || customer?.id || '',
        ChatRoomId: roomId,
        sent: true,
        received: false,
      });

      // Scroll to bottom after sending
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      console.log('Message sent successfully');

    } catch (error) {
      console.error('Error sending message:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi tin nhắn',
      });
    }
  }, [roomId, customer, dispatch]);

  const onLoadEarlier = useCallback(async () => {
    if (chatState.loading || !hasMoreMessages) return;

    try {
      const nextPage = page + 1;
      console.log('Loading earlier messages, page:', nextPage, 'current messages:', messages.length);

      const response = await ChatAPI.getMessages(roomId, nextPage);

      if (response.data.length === 0) {
        console.log('No more messages, disabling load earlier');
        setHasMoreMessages(false);
      } else {
        console.log('Loaded', response.data.length, 'earlier messages');
        setPage(nextPage);
        // Messages will be added via fetchMessages action
        dispatch(fetchMessages(roomId, nextPage));
      }
    } catch (error) {
      console.error('Error loading earlier messages:', error);
      setHasMoreMessages(false); // Disable on error
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải tin nhắn cũ',
      });
    }
  }, [roomId, page, hasMoreMessages, chatState.loading, dispatch, messages.length]);

  // Only show load earlier if we have enough messages and there might be more
  const shouldShowLoadEarlier = messages.length >= 10 && hasMoreMessages;

  const handleLibraryPicker = async () => {
    try {
      const permissions = await PermissionHelper.checkAllChatPermissions();
      console.log('Library permissions:', permissions);
      pickImageFromLibrary();
    } catch (error) {
      console.error('Error in handleLibraryPicker:', error);
      Alert.alert('Lỗi', 'Không thể mở thư viện ảnh');
    }
  };

  const handleCameraPicker = async () => {
    try {
      const permissions = await PermissionHelper.checkAllChatPermissions();
      console.log('Camera permissions:', permissions);
      pickImageFromCamera();
    } catch (error) {
      console.error('Error in handleCameraPicker:', error);
      Alert.alert('Lỗi', 'Không thể mở camera');
    }
  };

  const pickImageFromLibrary = () => {
    console.log('Opening image picker from library...');
    ImagePicker.openPicker({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
      compressImageQuality: 0.8,
      multiple: true,
    }).then(async (image) => {
      console.log('Image selected:', image);
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Image picker error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chọn ảnh từ thư viện',
        });
      }
    });
  };

  const pickImageFromCamera = () => {
    console.log('Opening camera...');
    ImagePicker.openCamera({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
      compressImageQuality: 0.8,
    }).then(async (image) => {
      console.log('Image captured:', image);
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Camera error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chụp ảnh',
        });
      }
    });
  };

  const sendImageMessage = async (image: any) => {
    try {
      // Upload image first
      let imageIds = [];
      if (Array.isArray(image)) {
        for (let i = 0; i < image.length; i++) {
          imageIds.push({
            uri: image[i].path,
            type: image[i].mime,
            name: image[i].filename ?? 'new file img',
          });
        }
      } else {
        imageIds.push({
          uri: image.path,
          type: image.mime,
          name: image.filename ?? 'new file img',
        });
      }
      const response = await BaseDA.uploadFiles(imageIds);
      if (response) {
        await handleSendMessage('', 2, response.map((img: any) => img.Id).join(','));
      }
    } catch (error) {
      console.error('Error sending image:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi ảnh',
      });
    }
  };

  const handleFilePicker = async () => {
    // Tạm thời disable file picker do vấn đề tương thích
    showSnackbar({
      status: ComponentStatus.INFOR,
      message: 'Tính năng gửi file sẽ được cập nhật trong phiên bản tiếp theo',
    });

    // TODO: Implement file picker with alternative solution
    // Options:
    // 1. Use react-native-image-picker for images/videos
    // 2. Use custom file browser
    // 3. Wait for react-native-document-picker update
  };

  // Custom input toolbar
  const renderInputToolbar = () => (
    <View style={styles.inputToolbarContainer}>
      <View style={styles.inputRow}>
        {/* Left side - Toolbar actions */}
        <View style={styles.leftToolbarContainer}>
          {!isInputFocused && !showToolbar ? (
            // Show toolbar icons when not focused and toolbar hidden
            <>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleLibraryPicker}>
                <Text style={styles.inputActionIcon}>🖼️</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleCameraPicker}>
                <Text style={styles.inputActionIcon}>📷</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleFilePicker}>
                <Text style={styles.inputActionIcon}>📎</Text>
              </TouchableOpacity>
            </>
          ) : isInputFocused ? (
            // Show expand button when focused
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setShowToolbar(true)}
            >
              <Text style={styles.toggleIcon}>{'>'}</Text>
            </TouchableOpacity>
          ) : showToolbar ? (
            // Show expanded toolbar
            <>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleLibraryPicker}>
                <Text style={styles.inputActionIcon}>🖼️</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleCameraPicker}>
                <Text style={styles.inputActionIcon}>📷</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.inputActionButton} onPress={handleFilePicker}>
                <Text style={styles.inputActionIcon}>📎</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.toggleButton}
                onPress={() => setShowToolbar(false)}
              >
                <Text style={styles.toggleIcon}>{'<'}</Text>
              </TouchableOpacity>
            </>
          ) : null}
        </View>

        {/* Input field với emoji bên trong */}
        <View style={[
          styles.inputContainer,
          (isInputFocused || showToolbar) && styles.inputContainerCompact
        ]}>
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={handleInputTextChange}
            placeholder="Bạn muốn nói gì?"
            placeholderTextColor={ColorThemes.light.neutral_text_title_color}
            multiline
            blurOnSubmit={false}
            onFocus={() => {
              setIsInputFocused(true);
            }}
            onBlur={() => {
              setIsInputFocused(false);
            }}
          />
          {/* Emoji button bên trong input */}
          <TouchableOpacity
            style={styles.emojiButtonInside}
            onPress={handleEmojiButtonPress}
          >
            <Text style={styles.emojiIconInside}>😊</Text>
          </TouchableOpacity>
        </View>

        {/* Send button bên phải input - luôn hiển thị khi có text */}
        {inputText.trim() && (
          <TouchableOpacity
            style={styles.sendButton}
            onPress={() => {
              if (inputText.trim()) {
                handleSendMessage(inputText);
              }
            }}
          >
            <Text style={styles.sendIcon}>➤</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );



  // Render individual message item
  const renderMessageItem = ({ item: message }: { item: ChatMessage }) => {
    const isMyMessage = message.CustomerId === (customer?.Id || customer?.id);
    const messageTime = new Date(message.DateCreated).toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    // Debug log để kiểm tra message
    console.log('Rendering message:', {
      Type: message.Type,
      TypeOf: typeof message.Type,
      FileUrl: message.FileUrl,
      Content: message.Content
    });

    return (
      <View style={[
        styles.messageContainer,
        isMyMessage ? styles.myMessageContainer : styles.otherMessageContainer
      ]}>
        {/* Avatar for other user messages */}
        {!isMyMessage && (
          <View style={styles.avatarContainer}>
            {message.user?.Avatar ? (
              <Image source={{ uri: ConfigAPI.urlImg + message.user.Avatar }} style={styles.messageAvatar} />
            ) : (
              <View style={[styles.messageAvatar, styles.defaultAvatar, {
                backgroundColor: getRandomAvatarColor(message.user?.Name || 'User')
              }]}>
                <Text style={styles.avatarText}>
                  {(message.user?.Name || 'U').charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Message bubble */}
        <View style={[
          message.Type === 2 ? {
            backgroundColor: 'white',
            padding: 0,
            margin: 0
          } :
            styles.messageBubble,
          isMyMessage ? styles.myMessageBubble : styles.otherMessageBubble
        ]}>
          {/* Message content based on type */}
          {message.Type === 2 && message.FileUrl ? (
            // Image message - support multiple images
            (() => {
              const imageIds = message.FileUrl.split(',').map(id => id.trim()).filter(id => id);
              const imageUrls = imageIds.map(id => ConfigAPI.urlImg + id);

              console.log('Image message debug:', {
                FileUrl: message.FileUrl,
                imageIds,
                imageUrls,
                ConfigAPI_urlImg: ConfigAPI.urlImg
              });

              if (imageIds.length === 1) {
                // Single image
                return (
                  <TouchableOpacity
                    style={styles.imageContainer}
                    onPress={() => {
                      console.log('Image pressed:', message.FileUrl);
                      setSelectedImages(imageUrls);
                      setSelectedImageIndex(0);
                      setShowImageViewer(true);
                    }}
                  >
                    <FastImage
                      source={{ uri: imageUrls[0] }}
                      style={styles.messageImage}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                  </TouchableOpacity>
                );
              } else {
                // Multiple images - 2x2 grid layout
                const displayImages = imageIds.slice(0, 4); // Show max 4 images
                const remainingCount = imageIds.length - 4;

                console.log('Rendering multiple images, displayImages:', displayImages);

                return (
                  <View style={styles.multipleImagesContainer}>

                    <View style={styles.imagesRow}>
                      {displayImages.slice(0, 2).map((imageId, index) => {
                        const imageUrl = ConfigAPI.urlImg + imageId;
                        console.log(`Rendering image ${index}:`, imageUrl);

                        return (
                          <TouchableOpacity
                            key={index}
                            style={styles.gridImageItem}
                            onPress={() => {
                              console.log('Image pressed:', imageId, 'index:', index);
                              setSelectedImages(imageUrls);
                              setSelectedImageIndex(index);
                              setShowImageViewer(true);
                            }}
                          >
                            <FastImage
                              source={{ uri: imageUrl }}
                              style={styles.gridImage}
                              resizeMode={FastImage.resizeMode.contain}
                              onError={() => console.log(`FastImage ${index} error`)}
                              onLoad={() => console.log(`FastImage ${index} loaded successfully`)}
                              onLoadStart={() => console.log(`FastImage ${index} load started`)}
                            />
                          </TouchableOpacity>
                        );
                      })}
                    </View>

                    {displayImages.length > 2 && (
                      <View style={styles.imagesRow}>
                        {displayImages.slice(2, 4).map((imageId, index) => {
                          const imageUrl = ConfigAPI.urlImg + imageId;
                          console.log(`Rendering image ${index + 2}:`, imageUrl);

                          return (
                            <TouchableOpacity
                              key={index + 2}
                              style={styles.gridImageItem}
                              onPress={() => {
                                console.log('Image pressed:', imageId, 'index:', index + 2);
                                setSelectedImages(imageUrls);
                                setSelectedImageIndex(index + 2);
                                setShowImageViewer(true);
                              }}
                            >
                              <FastImage
                                source={{ uri: imageUrl }}
                                style={styles.gridImage}
                                resizeMode={FastImage.resizeMode.contain}
                                onError={() => console.log(`FastImage ${index + 2} error`)}
                                onLoad={() => console.log(`FastImage ${index + 2} loaded successfully`)}
                                onLoadStart={() => console.log(`FastImage ${index + 2} load started`)}
                              />
                              {/* Show count overlay on last image if more images exist */}
                              {remainingCount > 0 && index === 1 && (
                                <View style={styles.imageCountOverlay}>
                                  <Text style={styles.imageCountText}>+{remainingCount}</Text>
                                </View>
                              )}
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    )}
                  </View>
                );
              }
            })()
          ) : message.Type === 3 && message.FileUrl ? (
            // File message
            <TouchableOpacity
              style={styles.fileContainer}
              onPress={() => {
                console.log('File pressed:', message.FileUrl);
                // TODO: Open file
              }}
            >
              <Text style={styles.fileIcon}>📎</Text>
              <Text style={styles.fileName}>{message.Content || 'File'}</Text>
            </TouchableOpacity>
          ) : (
            // Text/Emoji message
            <Text style={[
              styles.messageText,
              isMyMessage ? styles.myMessageText : styles.otherMessageText
            ]}>
              {message.Content}
            </Text>
          )}

          {/* Message time
          <Text style={[
            styles.messageTime,
            isMyMessage ? styles.myMessageTime : styles.otherMessageTime
          ]}>
            {messageTime}
          </Text> */}
        </View>

        {/* Spacer for my messages */}
        {/* {isMyMessage && <View style={styles.messageSpacer} />} */}
      </View>
    );
  };

  const handleEmojiSelect = (emoji: string) => {
    console.log('Emoji selected:', emoji);

    // If user wants to send emoji immediately (you can change this behavior)
    if (inputText.trim() === '') {
      // Send emoji as separate message
      handleSendMessage(emoji, 4);
      setShowEmojiPicker(false);
    } else {
      // Add emoji to current input text
      setInputText(prev => prev + emoji);
    }
  };

  const handleEmojiButtonPress = () => {
    console.log('Emoji button pressed, current showEmojiPicker:', showEmojiPicker);
    setShowEmojiPicker(true);
  };

  const handleInputTextChange = (text: string) => {
    setInputText(text);

    // Send typing indicator
    if (text.length > 0) {
      SocketService.sendTyping(roomId);
    }
  };

  // Render empty state when no messages
  const renderEmptyState = () => {
    const roomName = room.Name || room.name || 'Người dùng';
    const roomAvatar = room.Avatar;
    const avatarColor = getRandomAvatarColor(roomName);

    return (
      <View style={styles.emptyStateContainer}>
        <View style={styles.emptyStateContent}>
          {roomAvatar ? (
            <FastImage source={{ uri: roomAvatar }} style={styles.emptyStateAvatar} />
          ) : (
            <View style={[styles.emptyStateAvatar, styles.emptyStateAvatarDefault, { backgroundColor: avatarColor }]}>
              <Text style={styles.emptyStateAvatarText}>
                {roomName.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <Text style={styles.emptyStateText}>Hãy bắt đầu trò chuyện</Text>
        </View>
      </View>
    );
  };

  // Image Viewer Component
  const renderImageViewer = () => {
    const screenWidth = Dimensions.get('window').width;
    const screenHeight = Dimensions.get('window').height;

    return (
      <Modal
        visible={showImageViewer}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageViewer(false)}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowImageViewer(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.imageCounter}>
              {selectedImageIndex + 1} / {selectedImages.length}
            </Text>
          </View>

          <View style={styles.imageViewerContent}>
            <FastImage
              source={{ uri: selectedImages[selectedImageIndex] }}
              style={[styles.fullScreenImage, { width: screenWidth, height: screenHeight * 0.8 }]}
              resizeMode={FastImage.resizeMode.contain}
            />
          </View>

          {selectedImages.length > 1 && (
            <View style={styles.imageViewerControls}>
              <TouchableOpacity
                style={[styles.navButton, selectedImageIndex === 0 && styles.navButtonDisabled]}
                onPress={() => {
                  if (selectedImageIndex > 0) {
                    setSelectedImageIndex(selectedImageIndex - 1);
                  }
                }}
                disabled={selectedImageIndex === 0}
              >
                <Text style={[styles.navButtonText, selectedImageIndex === 0 && styles.navButtonTextDisabled]}>
                  ← Trước
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.navButton, selectedImageIndex === selectedImages.length - 1 && styles.navButtonDisabled]}
                onPress={() => {
                  if (selectedImageIndex < selectedImages.length - 1) {
                    setSelectedImageIndex(selectedImageIndex + 1);
                  }
                }}
                disabled={selectedImageIndex === selectedImages.length - 1}
              >
                <Text style={[styles.navButtonText, selectedImageIndex === selectedImages.length - 1 && styles.navButtonTextDisabled]}>
                  Sau →
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <InforHeader title={room.Name || room.name || 'Người dùng'} />

      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <KeyboardAvoidingView
          style={styles.chatContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          {/* Messages list */}
          <FlatList
            ref={flatListRef}
            data={displayMessages}
            renderItem={renderMessageItem}
            keyExtractor={(item) => item.Id}
            style={styles.messagesList}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
            inverted={false}
            onStartReached={() => {
              if (shouldShowLoadEarlier && !chatState.loading) {
                console.log('Loading earlier messages...');
                onLoadEarlier();
              }
            }}
            onStartReachedThreshold={0.1}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 10,
            }}
            ListEmptyComponent={renderEmptyState}
            ListHeaderComponent={
              chatState.loading ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Đang tải tin nhắn cũ...</Text>
                </View>
              ) : null
            }
            ListFooterComponent={
              isTyping ? (
                <View style={styles.typingContainer}>
                  <Text style={styles.typingText}>Đang gõ...</Text>
                </View>
              ) : null
            }
            onContentSizeChange={() => {
              // Auto scroll to bottom when new message arrives
              if (displayMessages.length > 0) {
                setTimeout(() => {
                  flatListRef.current?.scrollToEnd({ animated: true });
                }, 100);
              }
            }}
          />

          {/* Input toolbar */}
          {renderInputToolbar()}

          {/* Emoji picker */}
          <EmojiPicker
            visible={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
            onEmojiSelect={handleEmojiSelect}
          />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>

      {/* Image Viewer */}
      {renderImageViewer()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    backgroundColor: ColorThemes.light.primary_color,
    paddingTop: Platform.OS === 'ios' ? 0 : 25,
    paddingBottom: 10,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  backIcon: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  headerAvatarDefault: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
  },
  headerInfo: {
    flex: 1,
  },
  headerName: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 2,
  },
  headerStatus: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 4,
  },
  headerButtonIcon: {
    fontSize: 20,
    color: 'white',
  },
  chatContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  messagesList: {
    flex: 1,
    // paddingHorizontal: 16,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 8,
  },
  myMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  messageBubble: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    marginHorizontal: 8,
  },
  myMessageBubble: {
    backgroundColor: '#0584FE',
    alignSelf: 'flex-end',
    maxWidth: '80%',
    marginRight: 8,
    marginLeft: 0,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  otherMessageBubble: {
    backgroundColor: '#F0F0F0',
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  myMessageText: {
    color: ColorThemes.light.white,
    paddingRight: 4,
    flexWrap: 'wrap',
    paddingLeft: 4,
  },
  otherMessageText: {
    color: ColorThemes.light.neutral_text_color,
  },
  messageTime: {
    fontSize: 11,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  myMessageTime: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  otherMessageTime: {
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  messageSpacer: {
    width: 40,
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxHeight: 100,
    color: ColorThemes.light.neutral_text_color,
  },
  inputToolbarContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_border_color,
    // paddingHorizontal: 8,
    paddingVertical: 8,
  },
  toolbarRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // paddingVertical: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  leftToolbarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  toggleButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  toggleIcon: {
    fontSize: 24,
    color: ColorThemes.light.primary_main_color,
  },
  inputActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 6,
  },
  inputActionIcon: {
    fontSize: 18,
    color: 'white',
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    borderWidth: 0.5,
    borderColor: '#ccc',
    paddingRight: 8,
  },
  inputContainerCompact: {
    flex: 1, // Thu nhỏ khi toolbar mở rộng
  },
  inputToolbar: {
    flex: 1,
    backgroundColor: 'transparent',
    borderTopWidth: 0,
    borderRadius: 20,
    minHeight: 40,
    marginVertical: 0,
  },
  inputPrimary: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  emojiButtonInside: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  emojiIconInside: {
    fontSize: 20,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendIcon: {
    color: ColorThemes.light.primary_main_color,
    fontSize: 18,
    fontWeight: 'bold',
  },
  bubbleRight: {
    backgroundColor: ColorThemes.light.primary_color,
    borderRadius: 18,
    marginVertical: 2,
    maxWidth: '80%',
    alignSelf: 'flex-end',
  },
  bubbleLeft: {
    backgroundColor: '#F0F0F0',
    borderRadius: 18,
    marginVertical: 2,
    maxWidth: '80%',
    alignSelf: 'flex-start',
  },
  textRight: {
    color: 'white',
    fontSize: 16,
    lineHeight: 20,
    flexWrap: 'wrap',
  },
  textLeft: {
    color: ColorThemes.light.neutral_text_color,
    fontSize: 16,
    lineHeight: 20,
    flexWrap: 'wrap',
  },
  scrollToBottomStyle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  scrollToBottomText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
  },
  typingText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    fontStyle: 'italic',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginVertical: 50,
  },
  emptyStateContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  emptyStateAvatarDefault: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateAvatarText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: 'white',
  },
  emptyStateText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    fontWeight: '500',
  },
  imageContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 4,
    padding: 2,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: ColorThemes.light.neutral_background_color,
    borderRadius: 8,
    marginVertical: 4,
    minWidth: 150,
  },
  fileIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  fileName: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_color,
    flex: 1,
  },
  // Multiple images styles
  multipleImagesContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 4,
    backgroundColor: 'transparent',
    padding: 2,
    maxWidth: 250,
    minHeight: 200, // Minimum height để đảm bảo hiển thị
    width: 250,
  },
  imagesRow: {
    flexDirection: 'row',
    marginBottom: 2,
    width: '100%',
    height: 100,
    backgroundColor: 'transparent',
  },
  gridImageItem: {
    flex: 1,
    height: 100, // Fixed height thay vì aspectRatio
    marginRight: 2,
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: 'transparent', // Fallback background
  },
  gridImage: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  // Legacy styles for backward compatibility
  multipleImageItem: {
    position: 'relative',
    borderRadius: 4,
    overflow: 'hidden',
    margin: 1,
  },
  multipleImage: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  twoImagesItem: {
    width: 120,
    height: 120,
  },
  threeImagesFirstItem: {
    width: 160,
    height: 120,
  },
  threeImagesOtherItem: {
    width: 78,
    height: 59,
  },
  fourImagesItem: {
    width: 78,
    height: 78,
  },
  imageCountOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
  },
  imageCountText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Image Viewer styles
  imageViewerContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerHeader: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  imageCounter: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  imageViewerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    borderRadius: 8,
  },
  imageViewerControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
  },
  navButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 25,
  },
  navButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  navButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  navButtonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
});

export default ChatRoomScreen;
