import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { ColorThemes } from '../../../assets/skin/colors';
import { navigate, RootScreen } from '../../../router/router';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import ChatAPI from '../services/ChatAPI';

interface Contact {
  Id: string;
  Name: string;
  Avatar?: string;
  IsOnline?: boolean;
  Mobile?: string;
  Email?: string;
  // Legacy fields for compatibility
  id?: string;
  name?: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  goldRank?: boolean;
  status?: string;
}

const ContactsScreen: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);

  const dispatch = useDispatch();
  const currentUser = useSelectorCustomerState().data;

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      const customers = await ChatAPI.getAllCustomers();

      // Lọc bỏ user hiện tại khỏi danh sách
      const filteredContacts = customers.filter(
        customer => customer.Id !== currentUser?.Id && customer.Id !== currentUser?.id
      );

      setContacts(filteredContacts);
    } catch (error) {
      console.error('Error loading contacts:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải danh bạ',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChatPress = async (contact: Contact) => {
    try {
      if (!currentUser?.Id && !currentUser?.id) {
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể xác định người dùng hiện tại',
        });
        return;
      }

      const currentUserId = currentUser.Id || currentUser.id!;
      const targetUserId = contact.Id;

      // Tìm hoặc tạo ChatRoom
      const chatRoom = await ChatAPI.findOrCreatePrivateRoom(currentUserId, contact);
      // Navigate đến ChatRoomScreen
      navigate(RootScreen.ChatRoom, { room: chatRoom });

    } catch (error) {
      console.error('Error creating chat:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tạo cuộc trò chuyện',
      });
    }
  };

  const handleCallPress = (contact: Contact) => {
    Alert.alert(
      'Cuộc gọi',
      `Gọi cho ${contact.Name}?`,
      [
        { text: 'Hủy', style: 'cancel' },
        { text: 'Gọi', onPress: () => {
          // TODO: Implement call functionality
          showSnackbar({
            status: ComponentStatus.INFOR,
            message: 'Tính năng cuộc gọi đang được phát triển',
          });
        }},
      ]
    );
  };

  const isContactOnline = (contactId: string) => {
    return onlineUsers.includes(contactId);
  };

  const renderContactItem = ({ item }: { item: Contact }) => (
    <View style={styles.contactItem}>
      <View style={styles.avatarContainer}>
        {item.Avatar ? (
          <FastImage source={{ uri: item.Avatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.defaultAvatar]}>
            <Text style={styles.avatarText}>
              {item.Name?.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {/* Online status indicator */}
        {isContactOnline(item.Id) && (
          <View style={styles.onlineIndicator} />
        )}
      </View>

      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{item.Name}</Text>
        <View style={styles.statusContainer}>
          <View style={styles.goldBadge}>
            <Text style={styles.goldIcon}>👑</Text>
            <Text style={styles.goldText}>Gold</Text>
          </View>
          <Text style={styles.statusText}>
            {isContactOnline(item.Id) ? 'Online' : '20 phút'}
          </Text>
        </View>
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleCallPress(item)}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>📞</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.chatButton]}
          onPress={() => handleChatPress(item)}
          activeOpacity={0.7}
        >
          <Text style={styles.actionIcon}>💬</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Chưa có liên hệ nào</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={contacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.Id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={contacts.length === 0 ? styles.emptyList : undefined}
        refreshing={loading}
        onRefresh={loadContacts}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  contactItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goldBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFD700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  goldIcon: {
    fontSize: 10,
    marginRight: 2,
  },
  goldText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#B8860B',
  },
  statusText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
  },
  chatButton: {
    backgroundColor: ColorThemes.light.primary_color,
    borderColor: ColorThemes.light.primary_color,
  },
  actionIcon: {
    fontSize: 16,
  },
});

export default ContactsScreen;
