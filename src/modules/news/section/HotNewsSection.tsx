import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet, TouchableOpacity} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import HotNewsCard from '../card/HotNewsCard';
import {NewsItem} from '../../../redux/models/news';
import {newsAction} from '../../../redux/actions/newsAction';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';

interface HotNewsSectionProps {
  title?: string;
  isRefresh?: boolean;
}

const HotNewsSection: React.FC<HotNewsSectionProps> = ({
  title = 'Tin tức hot',
  isRefresh = false,
}) => {
  const navigation = useNavigation<any>();
  const [hotNews, setHotNews] = useState<NewsItem[]>([]);

  useEffect(() => {
    initData();
  }, [isRefresh]);

  const initData = async () => {
    const response = await newsAction.fetch({
      page: 1,
      size: 5,
      sortby: [{prop: 'Views', direction: 'DESC'}],
    });
    if (response.data.length > 0) setHotNews(response.data);
  };

  // xem thêm
  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  // chuyến đến trang chi tiết
  const onNavigateToDetail = (item: NewsItem) => {
    navigation.navigate(RootScreen.DetailNews, {id: item.Id});
  };

  // Không hiển thị gì nếu không có dữ liệu
  if (!hotNews || hotNews.length === 0) {
    return null;
  }

  return (
    <View style={styles.sectionContainer}>
      {/* Header */}
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {
          <TouchableOpacity onPress={onSeeMore}>
            <Text style={styles.seeMoreButton}>Xem thêm</Text>
          </TouchableOpacity>
        }
      </View>

      {/* News List */}
      <FlatList
        data={hotNews}
        renderItem={({item}) => (
          <HotNewsCard item={item} onPress={() => onNavigateToDetail(item)} />
        )}
        keyExtractor={item => item.Id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContentContainer}
      />
    </View>
  );
};

export default HotNewsSection;

// ====== STYLES ======
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f0f2f5',
  },
  sectionContainer: {
    marginTop: 20,
    backgroundColor: '#ffffff',
    paddingVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.infor_text_color,
  },
  seeMoreButton: {
    fontSize: 16,
    color: '#007bff',
  },
  listContentContainer: {
    paddingLeft: 15,
  },
  cardContainer: {
    width: 160,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: 100,
  },
  cardTextContainer: {
    padding: 10,
  },
  cardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1c1e21',
    marginBottom: 4,
    height: 44,
  },
  cardType: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 6,
    alignSelf: 'flex-start', // Làm cho background vừa với text
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    overflow: 'hidden',
  },

  cardTimestamp: {
    fontSize: 12,
    color: '#8a8d91',
  },
});
