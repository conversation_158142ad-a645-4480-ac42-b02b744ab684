import {useCallback, useState} from 'react';
import {DataController} from '../../../base/baseController';

const bannerDA = new DataController('Banner');

export const useBanners = () => {
  const [banners, setBanners] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(false);

  const fetchBanners = useCallback(async () => {
    setLoading(true);
    try {
      const result = await bannerDA.getListSimple({
        page: 1,
        size: 10,
        query: '@Type: [2 2] @IsShowHome: {true}',
      });
      if (result && result.data) {
        setBanners(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch banners:', error);
      // Handle error appropriately in a real app
    } finally {
      setLoading(false);
    }
  }, []);

  return {banners, isLoading, fetchBanners};
};
