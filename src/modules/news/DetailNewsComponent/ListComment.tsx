import React from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import iconSvg from '../../../svg/icon';
import {AppSvg} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';

interface User {
  name: string;
  avatar: string;
  group?: string;
}

interface Comment {
  id: string;
  user: User;
  timestamp: string;
  commentText: string;
  likes: number;
  hasReplies: boolean;
  numberOfReplies: number;
}

interface CommentItemProps {
  comment: Comment;
}

const commentsData: Comment[] = [
  {
    id: '1',
    user: {
      name: '<PERSON><PERSON>n',
      avatar:
        'https://i.pinimg.com/736x/b7/91/44/b79144e03dc4996ce319ff59118caf65.jpg',
      group: 'Ruby',
    },
    timestamp: '20 phút',
    commentText: '<PERSON><PERSON>i người vào đây rôm rả nào',
    likes: 10,
    hasReplies: false,
    numberOfReplies: 0,
  },
  {
    id: '2',
    user: {
      name: 'Linh Lan',
      avatar:
        'https://i.pinimg.com/736x/b7/91/44/b79144e03dc4996ce319ff59118caf65.jpg',
      group: 'KTX Group',
    },
    timestamp: '20 phút',
    commentText: 'Mọi người vào đây rôm rả nào',
    likes: 10,
    hasReplies: true,
    numberOfReplies: 10,
  },
];

const CommentComponent: React.FC<CommentItemProps> = ({comment}) => {
  return (
    <View style={styles.commentContainer}>
      <Image source={{uri: comment.user.avatar}} style={styles.avatar} />
      <View>
        <View style={styles.commentContent}>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{comment.user.name}</Text>
          </View>
          <Text style={styles.commentText}>{comment.commentText}</Text>
        </View>
        <View style={styles.actions}>
          <TouchableOpacity style={styles.actionButton}>
            <AppSvg SvgSrc={iconSvg.thumbUp} size={16} />
            <Text style={styles.actionText}>{comment.likes}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Text style={styles.actionText}>Trả lời</Text>
          </TouchableOpacity>
          <Text style={styles.actionText}>Vừa xong</Text>
        </View>
        {comment.hasReplies && comment.numberOfReplies > 0 && (
          <TouchableOpacity style={styles.viewRepliesButton}>
            <AppSvg SvgSrc={iconSvg.curvedRightArrow} size={20} />
            <Text style={styles.viewRepliesText}>Xem các phản hồi</Text>
            <Text style={styles.viewRepliesTextNumber}>
              - {comment.numberOfReplies} phản hồi
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const ListComment: React.FC = () => {
  return (
    <View>
      {commentsData.map(comment => (
        <CommentComponent key={comment.id} comment={comment} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  commentContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  commentContent: {
    backgroundColor: ColorThemes.light.neutral_disable_background_color,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 3,
  },
  userName: {
    ...TypoSkin.title5,
    color: ColorThemes.light.infor_main_color,
    fontWeight: 'bold',
    marginRight: 5,
  },
  commentText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 5,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  actionText: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
  viewRepliesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  viewRepliesText: {
    marginLeft: 5,
    ...TypoSkin.body3,
    fontWeight: 'bold',
  },
  viewRepliesTextNumber: {
    ...TypoSkin.label3,
    marginLeft: 5,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default ListComment;
