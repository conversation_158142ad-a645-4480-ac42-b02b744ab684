import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

interface ListHastTagProps {
  hashtags: string;
}

const ListHastTag = ({hashtags}: ListHastTagProps) => {
  const hashtagList = hashtags
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);

  if (hashtagList.length === 0) {
    return null;
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}>
      {hashtagList.map((tag, index) => (
        <View key={index} style={styles.hashtagItem}>
          <Text style={styles.hashtagText}>
            {tag.startsWith('#') ? tag : `#${tag}`}
          </Text>
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingVertical: 16,
    gap: 8,
  },
  hashtagItem: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  hashtagText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.primary_sub_color,
  },
});

export default ListHastTag;
