import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import ScreenHeader from '../../Screen/Layout/header';
import {ColorThemes} from '../../assets/skin/colors';
import {ScrollView, View, Text, StyleSheet, Dimensions} from 'react-native';
import {TypoSkin} from '../../assets/skin/typography';
import RenderHTML from 'react-native-render-html';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';

const {width} = Dimensions.get('window');

export default function DetailPost() {
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const item = route.params.item;

  const htmlContentWidth = width - 32; // Account for padding

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        width: '100%',
      }}>
      <ScreenHeader onBack={() => navigation.goBack()} title={item.Name} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        {/* Post Image */}
        {item.Img && (
          <View style={styles.imageContainer}>
            <FastImage
              source={{uri: item.Img}}
              style={styles.postImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Post Content */}
        <View style={styles.contentWrapper}>
          {/* Post Name/Title */}
          <Text style={styles.postTitle}>{item.Name}</Text>

          {/* Post Description */}
          {item.Description && (
            <Text style={styles.postDescription}>{item.Description}</Text>
          )}

          {/* Post Content (HTML) */}
          {item.Content && (
            <View style={styles.htmlContentContainer}>
              <RenderHTML
                contentWidth={htmlContentWidth}
                source={{html: item.Content}}
                tagsStyles={{
                  p: {
                    fontFamily: 'Inter',
                    fontSize: 14,
                    lineHeight: 22,
                    color: ColorThemes.light.neutral_main_text_color,
                    marginBottom: 12,
                  },
                  h1: {
                    fontFamily: 'Inter',
                    fontSize: 20,
                    fontWeight: '600',
                    color: ColorThemes.light.neutral_main_text_color,
                    marginBottom: 16,
                    marginTop: 16,
                  },
                  h2: {
                    fontFamily: 'Inter',
                    fontSize: 18,
                    fontWeight: '600',
                    color: ColorThemes.light.neutral_main_text_color,
                    marginBottom: 12,
                    marginTop: 12,
                  },
                  h3: {
                    fontFamily: 'Inter',
                    fontSize: 16,
                    fontWeight: '600',
                    color: ColorThemes.light.neutral_main_text_color,
                    marginBottom: 8,
                    marginTop: 8,
                  },
                  img: {
                    marginVertical: 12,
                  },
                  ul: {
                    marginBottom: 12,
                  },
                  ol: {
                    marginBottom: 12,
                  },
                  li: {
                    fontFamily: 'Inter',
                    fontSize: 14,
                    lineHeight: 22,
                    color: ColorThemes.light.neutral_main_text_color,
                    marginBottom: 4,
                  },
                }}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  imageContainer: {
    width: '100%',
    height: 250,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  postImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  contentWrapper: {
    paddingHorizontal: 16,
  },
  postTitle: {
    ...TypoSkin.semibold4,
    color: ColorThemes.light.neutral_main_text_color,
    marginBottom: 12,
  },
  postDescription: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_sub_text_color,
    marginBottom: 16,
    lineHeight: 22,
  },
  htmlContentContainer: {
    marginTop: 8,
  },
});
