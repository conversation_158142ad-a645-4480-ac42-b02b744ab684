import React, {useEffect, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppSvg} from 'wini-mobile-components';

import iconSvg from '../../svg/icon';
import {ColorThemes} from '../../assets/skin/colors';
import {NewsItem} from '../../redux/models/news';
import ActionBar from './DetailNewsComponent/ActionBar';
import BasicInfoNews from './DetailNewsComponent/BasicInfoNews';
import Content from './DetailNewsComponent/Content';
import ListHastTag from './DetailNewsComponent/ListHastTag';
import ListComment from './DetailNewsComponent/ListComment';
import {newsAction} from '../../redux/actions/newsAction';

const hashtags = '<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>';

const DetailNews = () => {
  const route = useRoute();
  const {id} = route.params as {id: string};
  console.log('newsId', id);
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [news, setNews] = useState<NewsItem | null>(null);

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await newsAction.fetchById(id);
      if (res) {
        setNews(res);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Button back luôn hiển thị ở góc */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        {/* Main Image */}
        <View>
          <Image source={{uri: news?.Img}} style={styles.mainImage} />
        </View>

        <View style={{marginHorizontal: 16}}>
          {/* các thao tác */}
          {news && <ActionBar data={news} />}

          {/* thông tin cơ bản */}
          {news && <BasicInfoNews postData={news} />}

          {/* nội dung */}
          {news && <Content data={news?.Content} />}

          {/* hashtag */}
          {news && <ListHastTag hashtags={hashtags} />}

          {/* bình luận */}
          {news && <ListComment />}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  backButton: {
    position: 'absolute',
    top: 40,
    left: 16,
    backgroundColor: ColorThemes.light.secondary1_sub_color,
    width: 35,
    height: 35,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    elevation: 5, // cho Android
  },
});

export default DetailNews;
