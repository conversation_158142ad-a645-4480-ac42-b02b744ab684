import React, {FC, useState, useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {TypoSkin} from '../../../../../assets/skin/typography';
import {ColorThemes} from '../../../../../assets/skin/colors';

interface CountdownTimerProps {
  TargetDate: number;
  textSize?: 'small' | 'large';
}

export const CountdownTimer: FC<CountdownTimerProps> = ({
  TargetDate,
  textSize = 'small',
}: CountdownTimerProps) => {
  const calculateTimeLeft = () => {
    const difference = TargetDate - Date.now();
    let timeLeft = {days: 0, hours: 0, minutes: 0};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
      };
    }
    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [TargetDate]);

  const getStyleCountdown = () => {
    if (textSize === 'small') {
      return styles.countdownValueSmall;
    }
    return styles.countdownValueLarge;
  };

  return (
    <View style={styles.countdownContainer}>
      <View style={styles.countdownItem}>
        <Text style={getStyleCountdown()}>{timeLeft.days}</Text>
        <Text style={getStyleCountdown()}>Days</Text>
      </View>
      <View style={styles.countdownSeparator} />
      <View style={styles.countdownItem}>
        <Text style={getStyleCountdown()}>{timeLeft.hours}</Text>
        <Text style={getStyleCountdown()}>Hours</Text>
      </View>
      <View style={styles.countdownSeparator} />
      <View style={styles.countdownItem}>
        <Text style={getStyleCountdown()}>{timeLeft.minutes}</Text>
        <Text style={getStyleCountdown()}>Minutes</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  countdownContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  countdownItem: {
    alignItems: 'center',
    marginHorizontal: 6,
  },
  countdownValueSmall: {
    ...TypoSkin.title4,
    color: ColorThemes.light.white,
    fontWeight: 'bold',
  },
  countdownLabelSmall: {
    ...TypoSkin.title5,
    color: ColorThemes.light.white,
    marginTop: 2,
    fontWeight: 'bold',
  },
  countdownValueLarge: {
    ...TypoSkin.title2,
    color: ColorThemes.light.white,
    fontWeight: 'bold',
  },
  countdownLabelLarge: {
    ...TypoSkin.title2,
    color: ColorThemes.light.white,
    marginTop: 2,
    fontWeight: 'bold',
  },
  countdownSeparator: {
    width: 1,
    height: '50%',
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
});
