import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  FlatList,
  StyleSheet,
  View,
  ActivityIndicator,
  Text,
  TouchableOpacity,
} from 'react-native';
import NewsCard from '../card/LatestNewsCard';
import {NewsItem} from '../../../redux/models/news';
import {useDispatch, useSelector} from 'react-redux';
import {ColorThemes} from '../../../assets/skin/colors';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {fetchNews, loadMoreNews} from '../../../redux/actions/newsAction';

const TabNews = () => {
  const dispatch: AppDispatch = useDispatch();
  const {data, loading, totalCount, loadingMore} = useSelector(
    (state: RootState) => state.news,
  );

  const [page, setPage] = useState(1);

  useEffect(() => {
    dispatch(fetchNews());
  }, [dispatch]);

  // load more tin tức
  const handleLoadMore = () => {
    dispatch(loadMoreNews({page: page + 1}));
    setPage(page + 1);
  };

  // đang tải dữ liệu
  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // không có dữ liệu
  if (data.length === 0) {
    return (
      <View style={styles.center}>
        <Text>Hiện tại chưa có tin tức nào</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        keyExtractor={item => item.Id}
        contentContainerStyle={styles.listContent}
        ItemSeparatorComponent={() => <View style={{height: 10}} />}
        renderItem={({item}: {item: NewsItem}) => <NewsCard item={item} />}
        ListFooterComponent={() =>
          data.length < totalCount ? (
            <View style={styles.loadMoreButtonContainer}>
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={handleLoadMore}
                disabled={loadingMore}>
                {loadingMore ? (
                  <ActivityIndicator color={ColorThemes.light.white} />
                ) : (
                  <Text style={styles.loadMoreButtonText}>Xem thêm</Text>
                )}
              </TouchableOpacity>
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  listContent: {
    paddingVertical: 16,
  },
  center: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  loadMoreButtonText: {
    color: ColorThemes.light.white,
    fontWeight: 'bold',
    fontSize: 12,
  },
  loadMoreButtonContainer: {
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadMoreButton: {
    height: 35,
    width: 100,
    backgroundColor: ColorThemes.light.primary_main_color,
    padding: 6,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default TabNews;
