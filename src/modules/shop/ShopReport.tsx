import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';

import ReviewItem from '../../components/shop/ReviewItem';
import {Title} from '../../Config/Contanst';
import Chart from '../../components/shop/ShopReport/Chart';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';

const ChartReport = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={Title.Report} />
      <Chart />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
});

export default ChartReport;
