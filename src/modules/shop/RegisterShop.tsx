/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  Animated,
  Dimensions,
  Linking,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import HeaderShop from '../../components/shop/HeaderShop';
import StoreInfoForm from '../../components/shop/InputShopInfo';
import {Title} from '../../Config/Contanst';
import {LeaderShopInfoData} from '../../components/dto/dto';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
const RegisterShop = () => {
  const route = useRoute<any>();
  const [statusInput, setStatusInput] = useState<string>('');
  const [data, setData] = useState<LeaderShopInfoData>();
  useEffect(() => {
    if (route?.params?.data) {
      if (route?.params?.status && route?.params?.status == 'edit') {
        setStatusInput(route?.params?.status);
      }
      if (route?.params?.data && route?.params?.data?.name) {
        setData(route?.params?.data);
      }
    }
  }),
    [route];

  return (
    <View style={styles.container}>
      {/* Header */}

      <InforHeader
        title={statusInput == 'edit' ? Title.Edit : Title.Register}
      />

      <StoreInfoForm
        statusInput={statusInput}
        data={data as LeaderShopInfoData}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },
});

export default RegisterShop;
