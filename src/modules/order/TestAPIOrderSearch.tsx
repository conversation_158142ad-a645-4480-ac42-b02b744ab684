import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import SearchBar from '../../components/shop/Search';
import { TypoSkin } from '../../assets/skin/typography';
import { ColorThemes } from '../../assets/skin/colors';
import { DataController } from '../../base/baseController';

const TestAPIOrderSearch = () => {
  const [dataSearch, setDataSearch] = useState<string>('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const orderController = new DataController('Order');

  const testAPISearch = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      setSearchQuery('');
      return;
    }

    setIsSearching(true);
    console.log('Testing API search with term:', searchTerm);

    try {
      // Tạo query tìm kiếm giống như trong OrderCustomerDetail
      let searchQuery = `@Status: [1]`; // Test với status = 1 (đơn hàng mới)
      
      const searchConditions = [];
      
      // Tìm kiếm theo mã đơn hàng
      searchConditions.push(`@Code:(*${searchTerm}*)`);
      
      // Tìm kiếm theo số tiền
      if (!isNaN(Number(searchTerm.replace(/[.,]/g, '')))) {
        const numericValue = searchTerm.replace(/[.,]/g, '');
        searchConditions.push(`@Value:[${numericValue}]`);
      }
      
      // Kết hợp các điều kiện tìm kiếm
      if (searchConditions.length > 0) {
        searchQuery += ` (${searchConditions.join(' | ')})`;
      }

      setSearchQuery(searchQuery);
      console.log('API Search query:', searchQuery);

      const response = await orderController.getPatternList({
        page: 1,
        size: 20,
        query: searchQuery,
        pattern: {
          CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
          ShopId: ['Id', 'Name', 'Avatar'],
          AddressId: ['Id', 'Address'],
        },
      });

      console.log('API Response:', response);

      if (response.code === 200) {
        const processedData = response.data.map((item: any) => {
          item.Shop = response.Shop.find((shop: any) => shop.Id == item.ShopId);
          return item;
        });

        setSearchResults(processedData);
        console.log('Processed results:', processedData.length);
      } else {
        console.error('API Error:', response);
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = (text: string) => {
    setDataSearch(text);
    
    // Debounce
    setTimeout(() => {
      testAPISearch(text);
    }, 800);
  };

  const testCases = [
    { label: 'Tìm theo mã đơn', value: 'ORD' },
    { label: 'Tìm theo số tiền', value: '100000' },
    { label: 'Tìm theo số tiền có dấu', value: '100.000' },
    { label: 'Test empty', value: '' },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test API Order Search</Text>
      
      <SearchBar 
        setDataSearch={handleSearch}
        placeholder="Nhập từ khóa để test API search"
      />

      <View style={styles.testCasesContainer}>
        <Text style={styles.sectionTitle}>Test Cases:</Text>
        {testCases.map((testCase, index) => (
          <TouchableOpacity
            key={index}
            style={styles.testCaseButton}
            onPress={() => handleSearch(testCase.value)}
          >
            <Text style={styles.testCaseText}>
              {testCase.label}: "{testCase.value}"
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Status: {isSearching ? 'Đang tìm kiếm...' : 'Sẵn sàng'}
        </Text>
        <Text style={styles.infoText}>
          Từ khóa: "{dataSearch}"
        </Text>
        <Text style={styles.infoText}>
          Kết quả: {searchResults.length} đơn hàng
        </Text>
      </View>

      {searchQuery && (
        <View style={styles.queryContainer}>
          <Text style={styles.queryTitle}>API Query:</Text>
          <Text style={styles.queryText}>{searchQuery}</Text>
        </View>
      )}

      <ScrollView style={styles.resultContainer}>
        {searchResults.map((item, index) => (
          <View key={item.Id} style={styles.orderItem}>
            <Text style={styles.orderCode}>Mã: {item.Code}</Text>
            <Text style={styles.orderShop}>Shop: {item.Shop?.Name || 'N/A'}</Text>
            <Text style={styles.orderValue}>Giá trị: {item.Value} đ</Text>
            <Text style={styles.orderStatus}>Status: {item.Status}</Text>
            <Text style={styles.orderId}>ID: {item.Id}</Text>
          </View>
        ))}
        
        {searchResults.length === 0 && dataSearch.trim() && !isSearching && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Không tìm thấy đơn hàng nào</Text>
          </View>
        )}
      </ScrollView>

      <View style={styles.instructionContainer}>
        <Text style={styles.instructionTitle}>Hướng dẫn:</Text>
        <Text style={styles.instructionText}>• Nhập mã đơn hàng để test</Text>
        <Text style={styles.instructionText}>• Nhập số tiền để test</Text>
        <Text style={styles.instructionText}>• Kiểm tra console logs để debug</Text>
        <Text style={styles.instructionText}>• API query sẽ hiển thị bên dưới</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    ...TypoSkin.heading5,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.primary_main_color,
  },
  testCasesContainer: {
    marginVertical: 10,
  },
  sectionTitle: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    marginBottom: 8,
  },
  testCaseButton: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    marginBottom: 4,
    borderRadius: 4,
  },
  testCaseText: {
    ...TypoSkin.regular3,
  },
  infoContainer: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  infoText: {
    ...TypoSkin.regular3,
    marginBottom: 2,
  },
  queryContainer: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
  },
  queryTitle: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    marginBottom: 4,
  },
  queryText: {
    ...TypoSkin.regular3,
    fontFamily: 'monospace',
    color: '#1976d2',
  },
  resultContainer: {
    flex: 1,
    marginTop: 10,
  },
  orderItem: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: ColorThemes.light.primary_main_color,
  },
  orderCode: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    color: ColorThemes.light.primary_main_color,
  },
  orderShop: {
    ...TypoSkin.regular3,
    marginTop: 2,
  },
  orderValue: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#DA251D',
    fontWeight: '600',
  },
  orderStatus: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#666',
  },
  orderId: {
    ...TypoSkin.regular3,
    marginTop: 2,
    color: '#999',
    fontSize: 11,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    ...TypoSkin.regular2,
    color: '#999',
  },
  instructionContainer: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#fff3e0',
    borderRadius: 8,
  },
  instructionTitle: {
    ...TypoSkin.regular2,
    fontWeight: '600',
    marginBottom: 8,
  },
  instructionText: {
    ...TypoSkin.regular3,
    marginBottom: 4,
  },
});

export default TestAPIOrderSearch;
