/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
  FlatList,
  RefreshControl,
} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {Title} from '../../Config/Contanst';
import SearchBar from '../../components/shop/Search';
import {DataController} from '../../base/baseController';
import {TypoSkin} from '../../assets/skin/typography';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {AppButton, FLoading, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {Ultis} from '../../utils/Utils';
import {navigate, RootScreen} from '../../router/router';

const OrderCustomerDetail = () => {
  const route = useRoute<any>();
  const [dataSearch, setDataSearch] = useState<string>('');
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMoreData, setHasMoreData] = useState<boolean>(true);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const orderController = new DataController('Order');
  const orderDetailController = new DataController('OrderDetail');
  const ratingController = new DataController('Rating');
  const CustomerInfo = useSelectorCustomerState().data;
  const PAGE_SIZE = 10;

  useEffect(() => {
    setIsLoading(true);
    getData(route?.params?.status, 1, true);
  }, []);

  // Effect để xử lý tìm kiếm với API
  useEffect(() => {
    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Debounce search
    const timeoutId = setTimeout(() => {
      if (dataSearch.trim()) {
        searchWithAPI(dataSearch.trim());
      } else {
        // Nếu không có search term, load lại data gốc
        getData(route?.params?.status, 1, true);
      }
    }, 800); // Tăng delay lên 800ms cho API call

    setSearchTimeout(timeoutId);

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [dataSearch]);

  // Hàm tìm kiếm với API
  const searchWithAPI = async (searchTerm: string) => {
    console.log('searchWithAPI called with term:', searchTerm);
    setIsSearching(true);
    setIsLoading(true);

    try {
      // Tạo query tìm kiếm cho API
      let searchQuery = `@CustomerId: {${CustomerInfo?.Id}} @Status: [${route?.params?.status}]`;

      // Thêm điều kiện tìm kiếm
      const searchConditions = [];

      // Tìm kiếm theo mã đơn hàng
      searchConditions.push(`@Code:(*${searchTerm}*)`);

      // Tìm kiếm theo số tiền
      if (!isNaN(Number(searchTerm.replace(/[.,]/g, '')))) {
        const numericValue = searchTerm.replace(/[.,]/g, '');
        searchConditions.push(`@Value:[${numericValue}]`);
      }

      // Kết hợp các điều kiện tìm kiếm
      if (searchConditions.length > 0) {
        searchQuery += ` (${searchConditions.join(' | ')})`;
      }

      console.log('Search query:', searchQuery);

      const response = await orderController.getPatternList({
        page: 1,
        size: PAGE_SIZE, // Tăng size khi search để có nhiều kết quả hơn
        query: searchQuery,
        pattern: {
          CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
          ShopId: ['Id', 'Name', 'Avatar'],
          AddressId: ['Id', 'Address'],
        },
      });

      if (response.code === 200) {
        // Process data giống như getData
        const processedData = response.data.map((item: any) => {
          item.Shop = response.Shop.find((shop: any) => shop.Id == item.ShopId);
          return item;
        });

        // Xử lý rating nếu cần
        if (route?.params?.status == 3) {
          const resRated = await ratingController.getListSimple({
            page: 1,
            size: PAGE_SIZE,
            query: `@OrderId: {${processedData
              .map((item: any) => item.Id)
              .join(' | ')}}`,
          });
          if (resRated.code == 200) {
            resRated.data.map((item: any) => {
              processedData.find(
                (order: any) => order.Id == item.OrderId,
              ).isRated = true;
            });
          }
        }

        // Lọc thêm ở client-side cho tên shop và ngày tháng (vì API không hỗ trợ)
        const filteredData = processedData.filter((item: any) => {
          const term = searchTerm.toLowerCase();

          // Kiểm tra xem có match với API search không (mã đơn hàng hoặc số tiền)
          const codeMatch = item.Code?.toLowerCase().includes(term);
          const valueMatch = item.Value?.toString().includes(
            term.replace(/[.,]/g, ''),
          );

          // Tìm kiếm theo tên shop
          const shopNameMatch = item.Shop?.Name?.toLowerCase().includes(term);

          // Tìm kiếm theo ngày tháng
          let dateMatch = false;
          if (item.DateCreated) {
            const formattedDate = Ultis.formatDateTime(item.DateCreated, true);
            dateMatch = formattedDate.toLowerCase().includes(term);

            const dateOnly = new Date(item.DateCreated).toLocaleDateString(
              'vi-VN',
            );
            dateMatch = dateMatch || dateOnly.includes(term);
          }

          return codeMatch || valueMatch || shopNameMatch || dateMatch;
        });

        // Sử dụng kết quả đã lọc
        const finalData = filteredData;

        setData(finalData);
        setCurrentPage(1);
        setHasMoreData(false); // Disable pagination khi search

        console.log('Search results:', finalData.length);
      } else {
        console.error('Search API error:', response);
        setData([]);
      }
    } catch (error) {
      console.error('Error searching orders:', error);
      setData([]);
    } finally {
      setIsLoading(false);
      setIsSearching(false);
    }
  };

  const getData = async (
    status: number,
    page: number = 1,
    isRefresh: boolean = false,
  ) => {
    const response = await orderController.getPatternList({
      page: page,
      size: PAGE_SIZE,
      query: `@CustomerId: {${CustomerInfo?.Id}} @Status: [${status}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
        ShopId: ['Id', 'Name', 'Avatar'],
        AddressId: ['Id', 'Address'],
      },
    });
    if (response.code == 200) {
      // add shop item by response.Shop
      const processedData = response.data.map((item: any) => {
        item.Shop = response.Shop.find((shop: any) => shop.Id == item.ShopId);

        return item;
      });

      if (route?.params?.status == 3) {
        var resRated = await ratingController.getListSimple({
          page: page,
          size: PAGE_SIZE,
          query: `@OrderId: {${processedData
            .map((item: any) => item.Id)
            .join(' | ')}}`,
        });
        if (resRated.code == 200) {
          resRated.data.map((item: any) => {
            processedData.find(
              (order: any) => order.Id == item.OrderId,
            ).isRated = true;
          });
        }
      }

      if (isRefresh || page === 1) {
        setData(processedData);
        setCurrentPage(1);
      } else {
        setData(prevData => [...prevData, ...processedData]);
      }

      // Check if there's more data to load
      setHasMoreData(processedData.length === PAGE_SIZE);
      setCurrentPage(page);
      setIsLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreData = async () => {
    // Không load more khi đang search
    if (!loadingMore && hasMoreData && !dataSearch.trim()) {
      setLoadingMore(true);
      await getData(route?.params?.status, currentPage + 1, false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Reset search khi refresh
      setDataSearch('');
      setHasMoreData(true);
      await getData(route?.params?.status, 1, true);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#0000ff" />
        <Text style={styles.loadingText}>Đang tải thêm...</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <FLoading visible={isLoading} />

      <InforHeader
        title={
          route?.params?.status == 1
            ? Title.New
            : route?.params?.status == 2
            ? Title.Processing
            : route?.params?.status == 3
            ? Title.Done
            : Title.Cancel
        }
      />
      <View style={{position: 'relative'}}>
        <SearchBar
          setDataSearch={setDataSearch}
          placeholder="Tìm theo mã đơn, cửa hàng, tiền hoặc thời gian"
        />
        {isSearching && (
          <View
            style={{
              position: 'absolute',
              right: 20,
              top: '50%',
              transform: [{translateY: -10}],
            }}>
            <ActivityIndicator size="small" color="#0000ff" />
          </View>
        )}
      </View>
      <View style={styles.orderInfo}>
        <Text style={styles.title}>Danh sách đơn hàng</Text>
        <Text style={styles.numberOrder}>
          {isLoading || isSearching
            ? 'Đang tải dữ liệu...'
            : data?.length > 0
            ? `${data?.length} đơn hàng${
                dataSearch.trim() ? ' (đã tìm thấy)' : ''
              }`
            : dataSearch.trim()
            ? 'Không tìm thấy đơn hàng nào'
            : 'Chưa có đơn hàng nào'}
        </Text>
      </View>
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        // show flatlist
        <FlatList
          data={data}
          style={{flex: 1, height: '100%'}}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          contentContainerStyle={{paddingHorizontal: 16, gap: 16}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0000ff']} // Android
              tintColor={'#0000ff'} // iOS
            />
          }
          onEndReached={loadMoreData}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          renderItem={({item}) => {
            return (
              <View style={{flex: 1}}>
                <ListTile
                  onPress={() => {
                    navigate(RootScreen.OrderDetailPage, {
                      orderId: item.Id,
                    });
                  }}
                  style={{
                    padding: 16,
                    borderWidth: 1,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                  }}
                  listtileStyle={{alignItems: 'flex-start'}}
                  title={
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <Winicon
                        src="fill/shopping/store"
                        size={16}
                        color={ColorThemes.light.primary_main_color}
                      />
                      <Text style={{...TypoSkin.title3, marginLeft: 8}}>
                        {item?.Shop?.Name}
                      </Text>
                    </View>
                  }
                  subtitle={
                    <View style={{gap: 8, paddingTop: 8}}>
                      <Text style={{...TypoSkin.title4, color: '#999'}}>
                        Mã đơn hàng: {item?.Code}
                      </Text>
                      <Text style={{...TypoSkin.title4, color: '#999'}}>
                        {item?.DateCreated
                          ? Ultis.formatDateTime(item?.DateCreated, true)
                          : ''}
                      </Text>
                    </View>
                  }
                  trailing={
                    <View
                      style={{
                        paddingTop: 4,
                        flex: 1,
                        justifyContent: 'space-between',
                        alignItems: 'flex-end',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color:
                            route?.params?.status == 1
                              ? ColorThemes.light.primary_main_color
                              : route?.params?.status == 2
                              ? ColorThemes.light.warning_main_color
                              : route?.params?.status == 3
                              ? ColorThemes.light.success_main_color
                              : ColorThemes.light.error_main_color,
                        }}>
                        {route?.params?.status == 1
                          ? 'Đơn hàng mới'
                          : route?.params?.status == 2
                          ? 'Đang xử lý'
                          : route?.params?.status == 3
                          ? 'Hoàn thành'
                          : 'Đã hủy'}
                      </Text>
                    </View>
                  }
                  bottom={
                    <View
                      style={{
                        paddingTop: 16,
                        flex: 1,
                        width: '100%',
                        alignItems: 'flex-end',
                        justifyContent: 'flex-end',
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'baseline',
                          gap: 4,
                        }}>
                        <Text
                          style={{
                            ...TypoSkin.title4,
                          }}>
                          Tổng tiền:
                        </Text>
                        <Text
                          style={{
                            ...TypoSkin.title3,
                            color: '#DA251D',
                          }}>
                          {Ultis.money(item?.Value)} đ
                        </Text>
                      </View>
                      {route?.params?.status == 3 && (
                        <AppButton
                          disabled={item.isRated}
                          title={'Đánh giá'}
                          onPress={async () => {
                            const res =
                              await orderDetailController.getListSimple({
                                page: 1,
                                size: 1000,
                                query: `@OrderId: {${item.Id}}`,
                              });
                            if (res.code === 200) {
                              item.ProductId = res.data.map(
                                (item: any) => item.ProductId,
                              );
                              navigate(RootScreen.CreateReviewOrder, {
                                Data: item,
                              });
                            }
                          }}
                          containerStyle={{
                            marginTop: 16,
                            paddingHorizontal: 24,
                          }}
                          height={26}
                          backgroundColor={'#FFC043'}
                          borderColor="transparent"
                          textStyle={{
                            ...TypoSkin.buttonText3,
                            fontSize: 12,
                            color: '#DA251D',
                          }}
                        />
                      )}
                    </View>
                  }
                />
              </View>
            );
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    ...TypoSkin.title3,
    fontWeight: '500',
    fontFamily: 'roboto',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 10,
    color: '#999',
    marginBottom: 8,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {
    ...TypoSkin.body3,
    color: '#999',
  },
});

export default OrderCustomerDetail;
