import React, {useState, useCallback, useEffect} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import ProductItem from '../FavoriteProductComponent/ProductItem';
import {FavoriteProduct} from '../../../redux/models/favoriteProduct';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';
import {getRandomObjects} from '../../../utils/arrayUtils';

// Header Component
interface HeaderProps {
  title: string;
  onSeeMore?: () => void;
}

const Header: React.FC<HeaderProps> = ({title, onSeeMore}) => (
  <View style={styles.header}>
    <Text style={styles.headerTitle}>{title}</Text>
    <TouchableOpacity onPress={onSeeMore}>
      <Text style={styles.seeMore}>Xem thêm</Text>
    </TouchableOpacity>
  </View>
);

// List Separator Component
const ItemSeparator: React.FC = () => <View style={styles.separator} />;

// Main Screen Component
const MostSearchedScreen: React.FC<{
  onSeeMore: () => void;
  onRefresh: boolean;
}> = ({onSeeMore, onRefresh}) => {
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    getData();
  }, [onRefresh]);

  const getData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 100,
    });
    if (data.length > 10) {
      data = getRandomObjects(data, 5);
    }
    setProducts(data);
  };

  const handleSeeMore = useCallback(() => {
    onSeeMore();
  }, [onSeeMore]);

  const renderItem = useCallback(
    ({item}: {item: Product}) => <ProductItem item={item} />,
    [],
  );

  const keyExtractor = useCallback((item: Product) => item.Id, []);

  return (
    <View style={styles.screen}>
      <StatusBar barStyle="dark-content" />
      <Header title="Được tìm kiếm nhiều" onSeeMore={handleSeeMore} />
      {products?.length !== 0 &&
        products.map((item: any) => <ProductItem key={item.Id} item={item} />)}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  screen: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  seeMore: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginLeft: 96,
  },
});

export default MostSearchedScreen;
