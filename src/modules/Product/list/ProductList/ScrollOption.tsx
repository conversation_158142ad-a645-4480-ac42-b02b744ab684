import {useCallback, useEffect} from 'react';
import ScrollableTabs from '../../../news/scrollable/ScrollableTabs';
import {faFire, faTruckFast} from '@fortawesome/free-solid-svg-icons';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';

const TABS_DATA = [
  {
    id: 'IsHot',
    label: 'HOT',
    icon: faFire,
  },
  {
    id: 'IsFreeShip',
    label: 'Freeship',
    icon: faTruckFast,
    color: '#3FB993',
  },
  // {
  //   id: 'new',
  //   label: 'Mới',
  //   icon: faBookOpen,
  // },
  // {
  //   id: 'favorite',
  //   label: 'Nhãn hàng ưa chuộng',
  //   icon: faHeart,
  // },
];

const ScrollOption = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // // Handle filter change
  const handleFilterChange = useCallback(
    (filterId: any) => {
      const newActiveFilters = {
        [filterId]: true,
      };
      productByCategoryHook.setData('filter', {
        ...filter,
        activeFilters: newActiveFilters,
      });
    },
    [filter, productByCategoryHook],
  );

  useEffect(() => {
    handleFilterChange('IsHot');
  }, []);

  return (
    <ScrollableTabs onChangeTab={handleFilterChange as any} data={TABS_DATA} />
  );
};

export default ScrollOption;
