import React from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const {width} = Dimensions.get('window');

const ProductListSkeleton = () => (
  <View style={styles.skeletonItem}>
    <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
      <SkeletonPlaceholder.Item width="100%" height={200} borderRadius={8} />
      <SkeletonPlaceholder.Item
        width="80%"
        height={16}
        borderRadius={4}
        marginTop={8}
      />
      <SkeletonPlaceholder.Item
        width="60%"
        height={14}
        borderRadius={4}
        marginTop={4}
      />
    </SkeletonPlaceholder>
  </View>
);

const styles = StyleSheet.create({
  skeletonItem: {
    width: (width - 48) / 2,
  },
});

export default ProductListSkeleton;
