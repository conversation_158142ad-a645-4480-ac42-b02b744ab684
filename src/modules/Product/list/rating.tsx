import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Text,
  Image,
  Dimensions,
} from 'react-native';
import {ListTile, Rating, Winicon} from 'wini-mobile-components';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useRoute} from '@react-navigation/native';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import RenderHTML from 'react-native-render-html';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import {ScrollView} from 'react-native-gesture-handler';
import FastImage from 'react-native-fast-image';
import ClickableImage from '../../../components/ClickableImage';

const RatingScreen = () => {
  const [reviews, setReviews] = useState<any[]>([]);
  const [totalRating, setTotalRating] = useState<any>({rate: 0, total: 0});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const route = useRoute<any>();
  const productId = route.params?.id;
  const ratingController = new DataController('Rating');
  const width = Dimensions.get('window').width;

  // Fetch reviews data
  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    setLoading(true);
    // call total rating
    const totalRating = await ratingController.group({
      reducers:
        'LOAD * GROUPBY 1 @ProductId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
      searchRaw: `@ProductId: {${productId}}`,
    });
    if (totalRating.code === 200) {
      setTotalRating({
        rate: parseFloat(totalRating.data[0]?.TotalRate),
        total: parseFloat(totalRating.data[0]?.CountRate),
      });
    }
    const res = await ratingController.getPatternList({
      page: 1,
      size: 100,
      query: `@ProductId: {${productId}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (res.code === 200) {
      // map again res.data with customer
      res.data = res.data.map((item: any) => {
        return {
          ...item,
          userName: res.Customer.find(
            (customer: any) => customer.Id == item.CustomerId,
          ).Name,
          userAvatar: `${
            ConfigAPI.urlImg +
            res.Customer.find((customer: any) => customer.Id == item.CustomerId)
              .AvatarUrl
          }`,
          date: Ultis.numberToTime(item.DateCreated, true),
        };
      });
      setReviews(res.data);
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchReviews();
  }, []);

  // Render stars based on rating
  const renderStars = (rating: number) => {
    return (
      <View style={styles.starsContainer}>
        <Rating value={rating} size={20} fillColor="#FFC043" />
      </View>
    );
  };

  // Render review item
  const renderReviewItem = ({item}: {item: any}) => (
    <ListTile
      leading={
        <View style={styles.avatarContainer}>
          {item.userAvatar ? (
            <Image
              source={{uri: item.userAvatar}}
              style={{width: 40, height: 40, borderRadius: 20}}
            />
          ) : (
            <Text style={styles.avatarText}>
              {item.userName?.charAt(0) || ''}
            </Text>
          )}
        </View>
      }
      title={
        <View style={styles.reviewHeader}>
          <Text style={styles.userName}>{item.userName || ''}</Text>
          {renderStars(item.Value)}
        </View>
      }
      subtitle={
        <View style={styles.reviewContent}>
          <Text style={styles.comment}>{item.Description}</Text>
          <RenderHTML contentWidth={width} source={{html: item.Content}} />
          {/* show Img  */}
          {item.ListImg?.length > 0 && (
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              style={{marginVertical: 8, height: 56}}
              contentContainerStyle={{flexGrow: 1, gap: 8}}>
              {item.ListImg?.length > 0 &&
                item.ListImg?.split(',')?.map(
                  (image: string, index: number) => (
                    <ClickableImage
                      key={`item-${index}`}
                      source={{uri: ConfigAPI.urlImg + image}}
                      style={{
                        width: 56,
                        height: 56,
                        borderRadius: 8,
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                      }}
                    />
                  ),
                )}
            </ScrollView>
          )}
          <Text style={styles.date}>{item.date}</Text>
        </View>
      }
      style={styles.listTile}
    />
  );

  return (
    <View style={styles.container}>
      <InforHeader title="Đánh giá sản phẩm" />

      {/* show rating star and total review  */}
      <View
        style={{
          paddingVertical: 8,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.neutral_main_border_color,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 16,
          }}>
          <View style={styles.starsContainer}>
            <Rating
              value={
                !isNaN(totalRating.rate / totalRating.total) &&
                totalRating.total > 0
                  ? totalRating.rate / totalRating.total
                  : 0
              }
              size={20}
              fillColor="#FFC043"
            />
            <Text style={{...TypoSkin.body2, marginLeft: 8}}>
              {!isNaN(totalRating.rate / totalRating.total) &&
              totalRating.total > 0
                ? (totalRating.rate / totalRating.total).toFixed(1)
                : '0.0'}
            </Text>
          </View>

          <Text style={styles.reviewCount}>
            Tổng {totalRating.total} đánh giá
          </Text>
        </View>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      ) : (
        <FlatList
          data={reviews}
          renderItem={renderReviewItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
            />
          }
          ListEmptyComponent={<EmptyPage title="Chưa có đánh giá nào" />}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  listTile: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  reviewHeader: {
    flexDirection: 'column',
    marginBottom: 4,
  },
  userName: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
    alignItems: 'center',
  },
  reviewContent: {
    marginTop: 4,
  },
  comment: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    marginBottom: 8,
  },
  date: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  headerContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_divider_color,
  },
  headerTitle: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  reviewCount: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
});

export default RatingScreen;
