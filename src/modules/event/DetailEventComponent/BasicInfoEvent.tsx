import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Linking} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import dayjs from 'dayjs';
import {TypoSkin} from '../../../assets/skin/typography';
import {StatusChipEvent} from '../card/EventCards';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {checkTimeStatus} from '../../../utils/timeUltis';
import BasePopupConfirm from '../../../components/Popup/BasePopupConfirm';
import {eventRegisterAction} from '../../../redux/actions/eventRegisterAction';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useNewsEventHook} from '../../../redux/reducers/NewsEventReducer';

const getButtonJoin = (data: NewsEvent) => {
  if (!data.EventLink) return null;

  const status = checkTimeStatus(data.DateStart, data.DateEnd);
  if (status === 'ended') return null;
  return (
    <TouchableOpacity
      onPress={() => {
        if (status === 'started') return;
        if (data.EventLink) Linking.openURL(data.EventLink);
      }}
      style={[
        styles.chipStatus,
        {
          backgroundColor:
            status === 'onGoing'
              ? ColorThemes.light.primary_darker_color
              : ColorThemes.light.neutral_text_disabled_color,
        },
      ]}>
      <Text style={styles.registeredText}>Tham gia trực tuyến</Text>
    </TouchableOpacity>
  );
};

function formatDatesFromTimestamps(
  dateStartTimestamp: number,
  dateEndTimestamp: number,
) {
  const startDate = dayjs(dateStartTimestamp);
  const endDate = dayjs(dateEndTimestamp);

  const formattedStartDate = startDate.format('DD/MM/YYYY');
  const formattedEndDate = endDate.format('DD/MM/YYYY');

  return `${formattedStartDate} - ${formattedEndDate}`;
}

interface BasicInfoEventProps {
  event: NewsEvent;
}

const BasicInfoEvent: React.FC<BasicInfoEventProps> = ({event}) => {
  const customerHook = useSelectorCustomerState();
  const [loading, setLoading] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const status = checkTimeStatus(event.DateStart, event.DateEnd);

  // Thêm Redux hooks để cập nhật item
  const currentItem = useSelector((state: RootState) => state.newsEvent.item);
  const {action} = useNewsEventHook();

  // đăng ký sự kiện
  const handleConfirm = async () => {
    try {
      setLoading(true);
      const customerId = customerHook.data?.Id;

      if (!customerId) return console.error('Customer ID not found');

      const dataCreate = {
        Status: 'registered',
        NewsEventId: event.Id,
        CustomerId: customerId,
      };

      await eventRegisterAction.create(dataCreate);

      // Cập nhật isRegistered = true trong Redux store
      if (currentItem) {
        action.setData({
          stateName: 'item',
          data: {
            ...currentItem,
            isRegistered: true,
          },
        });
      }

      setShowConfirm(false);
    } catch (error) {
      console.error('Error registering for event:', error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <View>
      {/* Title */}
      <Text style={styles.title}>{event?.Title || ''}</Text>

      {/* Description */}
      <Text style={styles.description}>{event?.Description || ''}</Text>

      <View style={styles.infoContainer}>
        <TouchableOpacity
          onPress={() => {
            if (event?.AddressLink) Linking.openURL(event.AddressLink);
          }}
          style={styles.infoRow}>
          <AppSvg SvgSrc={iconSvg.pinmap} size={20} />
          <Text style={styles.infoText}>{event?.Address || ''}</Text>
        </TouchableOpacity>
        <View style={styles.infoRow}>
          <AppSvg SvgSrc={iconSvg.calendarColor} size={20} />
          <Text style={styles.infoText}>
            {formatDatesFromTimestamps(
              event?.DateStart || 0,
              event?.DateEnd || 0,
            )}
          </Text>
        </View>
      </View>

      <View style={{flexDirection: 'row', gap: 8, marginTop: 8}}>
        {getButtonJoin(event)}
        {status !== 'onGoing' && (
          <StatusChipEvent
            item={event}
            onRegister={() => setShowConfirm(true)}
            onJoin={() => {}}
          />
        )}
      </View>
      <BasePopupConfirm
        visible={showConfirm}
        loading={loading}
        title="Xác nhận đăng ký"
        message="Xác nhận đăng ký tham gia sự kiện này"
        onCancel={() => setShowConfirm(false)}
        onConfirm={handleConfirm}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    ...TypoSkin.title1,
  },
  description: {
    ...TypoSkin.title5,
    marginTop: 10,
  },
  infoContainer: {
    justifyContent: 'center',
    marginTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    marginLeft: 8,
    fontSize: 16,
    color: ColorThemes.light.primary_darker_color,
  },
  chipStatus: {
    height: 26,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  registeredText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.white,
  },
});

export default BasicInfoEvent;
