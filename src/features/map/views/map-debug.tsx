import React, {useEffect, useRef, useState} from 'react';
import MapView, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {
  Alert,
  Dimensions,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Geolocation from '@react-native-community/geolocation';

export default function MapDebug() {
  const mapRef = useRef<any>(null);
  const [currentRegion, setCurrentRegion] = useState({
    latitude: 21.040531,
    longitude: 105.774083,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [mapReady, setMapReady] = useState(false);
  const [locationPermission, setLocationPermission] = useState('unknown');

  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = () => {
    Geolocation.getCurrentPosition(
      position => {
        console.log('Location permission granted:', position);
        setLocationPermission('granted');
        const newRegion = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setCurrentRegion(newRegion);
      },
      error => {
        console.log('Location permission error:', error);
        setLocationPermission('denied');
        Alert.alert(
          'Location Permission',
          `Error: ${error.message}. Using default location (Hanoi).`,
        );
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      },
    );
  };

  const testMapFunctions = () => {
    if (!mapRef.current) {
      Alert.alert('Error', 'Map reference is null');
      return;
    }

    if (!mapReady) {
      Alert.alert('Error', 'Map is not ready yet');
      return;
    }

    // Test animateToRegion
    const testRegion = {
      latitude: 21.040531,
      longitude: 105.774083,
      latitudeDelta: 0.005,
      longitudeDelta: 0.005,
    };

    mapRef.current.animateToRegion(testRegion, 1000);
    Alert.alert('Success', 'Map animation triggered');
  };

  return (
    <View style={styles.container}>
      <View style={styles.debugInfo}>
        <Text style={styles.debugText}>Map Ready: {mapReady ? 'Yes' : 'No'}</Text>
        <Text style={styles.debugText}>Location: {locationPermission}</Text>
        <Text style={styles.debugText}>
          Coords: {currentRegion.latitude.toFixed(6)}, {currentRegion.longitude.toFixed(6)}
        </Text>
        <Text style={styles.debugText}>Platform: {Platform.OS}</Text>
      </View>

      <TouchableOpacity style={styles.testButton} onPress={testMapFunctions}>
        <Text style={styles.buttonText}>Test Map Functions</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.testButton} onPress={checkLocationPermission}>
        <Text style={styles.buttonText}>Refresh Location</Text>
      </TouchableOpacity>

      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={currentRegion}
        zoomEnabled={true}
        zoomControlEnabled={true}
        showsUserLocation={true}
        showsMyLocationButton={true}
        onMapReady={() => {
          console.log('Map is ready');
          setMapReady(true);
        }}
        onRegionChange={(region) => {
          console.log('Region changed:', region);
        }}
        onPress={(event) => {
          console.log('Map pressed:', event.nativeEvent.coordinate);
        }}>
        <Marker
          title="Test Marker"
          description="This is a test marker"
          coordinate={currentRegion}
          onPress={() => {
            Alert.alert('Marker Pressed', 'Marker interaction works!');
          }}
        />
      </MapView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  debugInfo: {
    position: 'absolute',
    top: 50,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 10,
    borderRadius: 8,
    zIndex: 1000,
  },
  debugText: {
    fontSize: 12,
    marginBottom: 2,
  },
  testButton: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    zIndex: 1000,
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});
