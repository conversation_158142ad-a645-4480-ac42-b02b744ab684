import React from 'react';
import { View, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import OTPInputComponent from '../../../components/OTPInputComponent';

interface Step2OTPVerificationProps {
  phoneNumber?: string;
  isVerifying: boolean;
  onOTPComplete: (otp: string) => void;
  onResendOTP: () => void;
  onVerify: () => void;
  otpValue: string;
}

const Step2OTPVerification: React.FC<Step2OTPVerificationProps> = ({
  phoneNumber,
  isVerifying,
  onOTPComplete,
  onResendOTP,
  onVerify,
  otpValue,
}) => {
  const isOTPComplete = otpValue.length === 6;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <OTPInputComponent
            length={6}
            onComplete={onOTPComplete}
            phoneNumber={phoneNumber}
            onResend={onResendOTP}
          />
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Xác nhận"
          onPress={onVerify}
          disabled={!isOTPComplete || isVerifying}
          backgroundColor={
            isOTPComplete && !isVerifying
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
  },
});

export default Step2OTPVerification;
