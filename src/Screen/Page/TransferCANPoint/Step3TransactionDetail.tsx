import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {App<PERSON>utton, showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import TransactionSummary from '../../../components/TransactionSummary';
import {TransactionType} from '../../../Config/Contanst';
import Clipboard from '@react-native-clipboard/clipboard';

interface Step3TransactionDetailProps {
  transactionData: {
    status: 'success' | 'failed';
    transactionId: string;
    amount: number;
    recipientName: string;
    recipientPhone: string;
    senderName: string;
    timestamp: string;
    type: TransactionType;
  };
  onDone: () => void;
}

const Step3TransactionDetail: React.FC<Step3TransactionDetailProps> = ({
  transactionData,
  onDone,
}) => {
  const handleCopyTransactionId = () => {
    Clipboard.setString(transactionData.transactionId);
    showSnackbar({
      message: 'Đã sao chép mã giao dịch',
      status: ComponentStatus.SUCCSESS,
    });
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <TransactionSummary
          accountNumber=""
          status={transactionData.status}
          transactionId={transactionData.transactionId}
          amount={transactionData.amount}
          recipientName={transactionData.recipientName}
          recipientPhone={transactionData.recipientPhone}
          senderName={transactionData.senderName}
          timestamp={transactionData.timestamp}
          onCopyTransactionId={handleCopyTransactionId}
          type={transactionData.type}
        />
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Đóng"
          onPress={onDone}
          backgroundColor={ColorThemes.light.primary_main_color}
          containerStyle={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
  },
});

export default Step3TransactionDetail;
