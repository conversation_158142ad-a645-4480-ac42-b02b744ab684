import React, {useState, useEffect, useCallback} from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';

import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {Winicon} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {DataController} from '../../base/baseController';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {Ultis} from '../../utils/Utils';
import {InforHeader} from '../Layout/headers/inforHeader';
import EmptyPage from '../emptyPage';

const {width} = Dimensions.get('window');

const TransactionHistory = () => {
  const navigation = useNavigation();
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [transactionHistory, settransactionHistory] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const customer = useSelectorCustomerState().data;

  const PAGE_SIZE = 10;
  const handleBack = () => {
    navigation.goBack();
  };

  // Tạo query filter dựa trên selectedFilter
  const getFilterQuery = () => {
    let baseQuery = `@CustomerId: {${customer?.Id}}`;
    if (selectedFilter === 'income') {
      baseQuery += ' @Value: [0 +inf]';
    } else if (selectedFilter === 'expense') {
      baseQuery += ' @Value: [-inf 0]';
    }
    return baseQuery;
  };

  // Hàm fetch data chính
  const fetchData = async (
    page: number = 1,
    isLoadMore: boolean = false,
    isRefresh: boolean = false,
  ) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const controller = new DataController('HistoryReward');
      const res = await controller.getListSimple({
        page: page,
        size: PAGE_SIZE,
        query: getFilterQuery(),
        returns: ['Id', 'Value', 'Description', 'DateCreated', 'Type'],
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (res.code === 200) {
        const mappedData = res.data.map((item: any) => ({
          id: item.Id,
          type: item.Value > 0 ? 'income' : 'expense',
          amount: item.Value,
          description: item.Description,
          time: item?.DateCreated
            ? Ultis.formatDateTime(parseInt(item?.DateCreated), true)
            : '',
          icon:
            item.Value > 0
              ? 'outline/arrows/arrow-bottom-left'
              : 'outline/arrows/arrow-top-right',
        }));

        if (isLoadMore) {
          settransactionHistory(prev => [...prev, ...mappedData]);
        } else {
          settransactionHistory(mappedData);
        }

        setTotalCount(res.totalCount || 0);
        setHasMore(
          mappedData.length === PAGE_SIZE &&
            (res.totalCount || 0) > page * PAGE_SIZE,
        );
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching transaction data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Hàm refresh data
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    setHasMore(true);
    fetchData(1, false, true);
  }, [selectedFilter, customer?.Id]);

  // Hàm load more data
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      const nextPage = currentPage + 1;
      fetchData(nextPage, true, false);
    }
  }, [currentPage, hasMore, loadingMore, selectedFilter, customer?.Id]);

  // Hàm xử lý thay đổi filter
  const handleFilterChange = useCallback(
    (filterId: string) => {
      setSelectedFilter(filterId);
      setCurrentPage(1);
      setHasMore(true);
      settransactionHistory([]);
      fetchData(1, false, false);
    },
    [customer?.Id],
  );

  // Load data khi component mount hoặc filter thay đổi
  useEffect(() => {
    if (customer?.Id) {
      fetchData(1, false, false);
    }
  }, [customer?.Id]);

  // Load data khi filter thay đổi
  useEffect(() => {
    if (customer?.Id && selectedFilter) {
      setCurrentPage(1);
      setHasMore(true);
      settransactionHistory([]);
      fetchData(1, false, false);
    }
  }, [selectedFilter]);

  const filterOptions = [
    {id: 'all', label: 'Tất cả', icon: 'fill/user interface/settings-gear'},
    {id: 'income', label: 'Vào', icon: 'outline/arrows/arrow-bottom-left'},
    {id: 'expense', label: 'Ra', icon: 'outline/arrows/arrow-top-right'},
  ];

  const renderFilterButton = (filter: any) => (
    <TouchableOpacity
      key={filter.id}
      style={[
        styles.filterButton,
        selectedFilter === filter.id && styles.filterButtonActive,
      ]}
      onPress={() => handleFilterChange(filter.id)}>
      <Winicon
        src={filter.icon}
        size={16}
        color={
          selectedFilter === filter.id
            ? 'white'
            : ColorThemes.light.neutral_text_title_color
        }
      />
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === filter.id && styles.filterButtonTextActive,
        ]}>
        {filter.label}
      </Text>
    </TouchableOpacity>
  );

  const renderTransactionItem = ({item}: any) => (
    <View style={styles.transactionItem}>
      <View
        style={[
          styles.transactionIcon,
          {
            backgroundColor:
              item.type === 'income'
                ? ColorThemes.light.secondary2_background
                : ColorThemes.light.error_background,
          },
        ]}>
        <Winicon
          src={item.icon}
          size={16}
          color={
            item.type === 'income'
              ? ColorThemes.light.secondary2_main_color
              : ColorThemes.light.error_main_color
          }
        />
      </View>
      <View style={styles.transactionContent}>
        <View style={styles.transactionHeader}>
          <Text
            style={[
              styles.transactionAmount,
              {
                color:
                  item.type === 'income'
                    ? ColorThemes.light.secondary2_main_color
                    : ColorThemes.light.error_main_color,
              },
            ]}>
            {item.type === 'income' ? '+' : '-'} {Ultis.money(item.amount)} GCT
          </Text>
        </View>
        {item.description && (
          <Text style={styles.transactionDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        <Text style={styles.transactionTime}>{item.time}</Text>
      </View>
    </View>
  );

  // Skeleton component cho loading state
  const TransactionSkeleton = () => (
    <View style={styles.transactionItem}>
      <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
        <SkeletonPlaceholder.Item
          width={40}
          height={40}
          borderRadius={20}
          marginRight={12}
        />
        <SkeletonPlaceholder.Item flex={1}>
          <SkeletonPlaceholder.Item
            width="60%"
            height={16}
            borderRadius={4}
            marginBottom={4}
          />
          <SkeletonPlaceholder.Item
            width="100%"
            height={14}
            borderRadius={4}
            marginBottom={4}
          />
          <SkeletonPlaceholder.Item width="40%" height={12} borderRadius={4} />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );

  // Render loading more indicator
  const renderLoadMoreIndicator = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.loadMoreContainer}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
        <Text style={styles.loadMoreText}>Đang tải thêm...</Text>
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    if (loading) return null;

    return (
      <View style={styles.emptyContainer}>
        <EmptyPage
          title="Không có giao dịch nào"
          subtitle={
            selectedFilter === 'all'
              ? 'Chưa có giao dịch nào được thực hiện'
              : `Không có giao dịch ${
                  selectedFilter === 'income' ? 'thu' : 'chi'
                } nào`
          }
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <InforHeader
        title={'Lịch sử giao dịch'}
        showAction
        customActions={
          <TouchableOpacity style={styles.searchButton}>
            <Winicon
              src="outline/user interface/search"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>
        }
      />

      {/* Filter Section */}
      <View style={styles.filterSection}>
        <View style={styles.filterContainer}>
          {filterOptions.map(renderFilterButton)}
        </View>
      </View>

      {/* Transaction Count */}
      <View style={styles.countSection}>
        <Text style={styles.countText}>Tổng {totalCount} giao dịch</Text>
      </View>

      {/* Transaction List */}
      {loading && transactionHistory.length === 0 ? (
        <View style={styles.listContainer}>
          {Array.from({length: 5}).map((_, index) => (
            <TransactionSkeleton key={index} />
          ))}
        </View>
      ) : (
        <FlatList
          data={transactionHistory}
          renderItem={renderTransactionItem}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.listContainer,
            transactionHistory.length === 0 && {flex: 1},
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={renderEmptyState}
          ListFooterComponent={renderLoadMoreIndicator}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  searchButton: {
    marginRight: 10,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'transparent',
  },
  filterSection: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.neutral_background_color,
    gap: 6,
  },
  filterButtonActive: {
    backgroundColor: ColorThemes.light.neutral_text_title_color,
  },
  filterButtonText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  countSection: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginBottom: 10,
  },
  countText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
  },
  listContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  transactionItem: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionContent: {
    flex: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  transactionDescription: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
    lineHeight: 20,
  },
  transactionTime: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  loadMoreText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default TransactionHistory;
