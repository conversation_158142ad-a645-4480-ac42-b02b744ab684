import React, {useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  StatusBar,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import {useNavigation} from '@react-navigation/native';
import {showSnackbar, ComponentStatus, FLoading} from 'wini-mobile-components';
import ScreenNewHeader from '../../Layout/header';
import StepIndicator from '../../../components/StepIndicator';
import Step1Introduction from './Step1Introduction';
import Step2PasswordVerification from './Step2PasswordVerification';
import Step3QRCodeSetup from './Step3QRCodeSetup';
import Step4VerificationCode from './Step4VerificationCode';
import {ColorThemes} from '../../../assets/skin/colors';
import {InforHeader} from '../../Layout/headers/inforHeader';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {Ultis} from '../../../utils/Utils';

const {width} = Dimensions.get('window');
const EmailAuthScreen = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  const customer = useSelectorCustomerState().data;
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [qrCodeValue, setQRCodeValue] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const totalSteps = 4;
  const [email, setEmail] = useState('');
  //dispatch
  const dispatch = useDispatch<AppDispatch>();

  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      pagerRef.current?.setPage(nextStep);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep);
    }
  };

  const handleStep1Next = async () => {
    goToNextStep();
  };

  const handleStep2Next = async (enteredPassword: string, email: string) => {
    if (!enteredPassword.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    if (!email.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập email',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    if (!Ultis.validateEmail(email)) {
      showSnackbar({
        message: 'Email không hợp lệ',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Verify password with API
      setPassword(enteredPassword);
      // check password
      const res = await CustomerActions.checkPassword(
        customer?.Mobile || '',
        enteredPassword,
      );
      if (res.code !== 200) {
        showSnackbar({
          message: 'Mật khẩu không chính xác',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      //kiểm tra email tồn tại
      const resEmail = await CustomerActions.checkEmail(email);
      if (resEmail.code !== 200) {
        showSnackbar({
          message: 'Email không tồn tại',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      //send mail
      setEmail(email);
      await CustomerActions.sendMail(email);
      goToNextStep();
    } catch (error) {
      showSnackbar({
        message: 'Mật khẩu không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };
  const handleStep4Complete = async (code: string) => {
    if (code.length !== 6) {
      showSnackbar({
        message: 'Vui lòng nhập đầy đủ mã xác thực',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    setIsLoading(true);
    try {
      // TODO: Verify OTP code with API
      const resVerify = await CustomerActions.verifyMail(email, code);
      if (resVerify.code !== 200) {
        showSnackbar({
          message: 'Mã xác thực không chính xác',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      showSnackbar({
        message: 'Xác minh tài khoản thành công!',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(CustomerActions.getInfor());
      // Navigate back or to success screen
      setCurrentStep(0);
      pagerRef.current?.setPage(0);
    } catch (error) {
      showSnackbar({
        message: 'Mã xác thực không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getHeaderTitle = () => {
    switch (currentStep) {
      case 0:
        return 'Xác thực tài khoản';
      case 1:
      case 2:
      case 3:
        return 'Xácthực OTP';
      default:
        return 'Xác thực tài khoản';
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      goToPreviousStep();
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <FLoading visible={isLoading} />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            <InforHeader title={'Xác thực tài khoản'} />

            {/* Step Indicator - Only show for steps 1, 2, 3 */}
            {currentStep > 0 && (
              <View style={styles.stepIndicatorContainer}>
                <StepIndicator currentStep={currentStep} totalSteps={3} />
              </View>
            )}

            <PagerView
              ref={pagerRef}
              style={styles.pagerView}
              initialPage={0}
              scrollEnabled={false}>
              <View key="step1" style={styles.pageContainer}>
                <Step1Introduction onNext={handleStep1Next} />
              </View>

              <View key="step2" style={styles.pageContainer}>
                <Step2PasswordVerification
                  onNext={(enteredPassword, email) =>
                    handleStep2Next(enteredPassword, email)
                  }
                  isLoading={isLoading}
                />
              </View>
              <View key="step3" style={styles.pageContainer}>
                <Step4VerificationCode
                  onComplete={handleStep4Complete}
                  isLoading={isLoading}
                />
              </View>
            </PagerView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
  },
  stepIndicatorContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  pagerView: {
    flex: 1,
    width: '90%',
    alignSelf: 'center',
    paddingHorizontal: 20,
  },
  pageContainer: {
    flex: 1,
    paddingBottom: Platform.OS === 'ios' ? 20 : 0, // Extra padding for iPhone X home indicator
  },
});

export default EmailAuthScreen;
