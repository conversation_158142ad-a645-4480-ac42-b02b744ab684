import {StyleSheet, Text, View} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
  TextField,
} from 'wini-mobile-components';
import Home from '../Page/Home';
import {ColorThemes} from '../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import CustomTabBar from '../../components/CustomTabBar';
import {navigateReset, RootScreen} from '../../router/router';
import Shop from '../../modules/shop/ManageShop';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch} from 'react-redux';
import ProductListByCategory from '../../modules/Product/list/ProductListByCategory';
import MyWallet from '../Page/myWallet';
import NewsScreen from '../../modules/news/NewsScreen';
import ChatMainScreen from '../../modules/chat/screens/ChatMainScreen';
import NewsIndex from '../../modules/news/newsIndex';
import {TypoSkin} from '../../assets/skin/typography';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useForm} from 'react-hook-form';
import {TextFieldForm} from '../../modules/Default/form/component-form';
import ProductIndex from '../../modules/Product/ProductIndex';
import {DataController} from '../../base/baseController';
import {CustomerStatus} from '../../Config/Contanst';
import {Ultis} from '../../utils/Utils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'home',
    component: Home,
  },
  {
    id: 5,
    name: 'news',
    component: NewsIndex,
  },
  {
    id: 1,
    name: 'products',
    component: ProductIndex,
  },
  {
    id: 2,
    name: 'QRCode',
    component: MyWallet,
  },
  {
    id: 3,
    name: 'community',
    component: Home,
  },
  {
    id: 6,
    name: 'chat',
    component: ChatMainScreen,
  },
  {
    id: 4,
    name: 'profile',
    component: Shop,
  },
];

export const dialogCheckAcc = (ref: any) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: 'Vui lòng đăng nhập để sử dụng!',
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function EComLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const isFirstTime = route.params?.isFirstTime;
  const [isShowDialog, setIsShowDialog] = useState<boolean>(
    isFirstTime ?? false,
  );
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {RefCode: ''},
  });
  const customerController = new DataController('Customer');

  useEffect(() => {
    if (user?.RefCode) setIsShowDialog(false);
  }, [user]);

  useEffect(() => {
    if (isShowDialog && user) {
      showDialog({
        ref: dialogCheckAccRef,
        status: ComponentStatus.INFOR,
        title: 'Chào mừng bạn đến với Chanivo',
        content: (
          <View style={{height: 140, gap: 8}}>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.neutral_main_color,
                textAlign: 'center',
              }}>
              Bạn đã đăng ký thành công! Nếu có người giới thiệu, hãy nhập mã
              code của người giới thiệu để được hưởng nhiều lợi nhuận hơn.
            </Text>
            <TextFieldForm
              control={methods.control}
              name="RefCode"
              placeholder="Nhập mã giới thiệu (Nếu có)"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              onBlur={async (ev: string) => {
                var code = ev.trim();
              }}
            />
          </View>
        ),
        onCancel: () => {
          setIsShowDialog(false);
          methods.setValue('RefCode', '');
        },
        onSubmit: async () => {
          const code = methods.watch('RefCode')?.trim();
          if (code === undefined || code?.length == 0) {
            return;
          }

          if (user) {
            //lấy thông tin theo ref code
            var parentId;
            var listParent;
            if (methods.watch('RefCode')?.length > 0) {
              const resRef = await customerController.getListSimple({
                page: 1,
                size: 1,
                query: `@RefCode: (*${methods.watch('RefCode')}*)`,
                returns: ['Id', 'Name', 'AvatarUrl', 'ListParent'],
              });
              if (resRef.code === 200 && resRef.data.length > 0) {
                parentId = resRef.data[0].Id;
                listParent = resRef.data[0].ListParent
                  ? resRef.data[0].ListParent + ',' + resRef.data[0].Id
                  : resRef.data[0].Id;
              }
            }

            const deviceToken = await getDataToAsyncStorage('fcmToken');

            dispatch(
              CustomerActions.edit({
                ...user,
                RanksData: undefined,
                Status: CustomerStatus.active,
                DeviceToken: deviceToken,
                RefCode: Ultis.randomString(10),
                ParentId: parentId,
                ListParent: listParent,
              }),
            );
            dispatch(CustomerActions.getInfor());
            showSnackbar({
              message: 'Cập nhật mã giới thiệu thành công',
              status: ComponentStatus.SUCCSESS,
            });
            setIsShowDialog(false);
            methods.setValue('RefCode', '');
          }
        },
      });
    }
  }, [isFirstTime, user]);

  return (
    <View style={styles.container}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          headerShown: false,
        }}
        tabBar={props => (
          <CustomTabBar props={props} ref={dialogCheckAccRef} />
        )}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.name !== 'news' &&
                    item.name !== 'home' &&
                    item.name !== 'profile' &&
                    item.name !== 'category' &&
                    item.name !== 'chat' &&
                    item.name !== 'products'
                  ) {
                    dialogCheckAcc(dialogCheckAccRef);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={{
                headerShown: false,
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingBottom: 16,
  },
});
