import { View, TouchableOpacity, Text, Image, StyleSheet } from 'react-native';
import React, { useState } from 'react';
import { RootScreen } from '../../../../router/router';
import { ListTile, Winicon } from 'wini-mobile-components';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { EComStackNavigator } from '../miniApp/ecomNavigator';
import { ColorThemes } from '../../../../assets/skin/colors';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';

const menuItems = [
  {
    title: 'E-Com',
    route: 'navigateEComView',
    icon: 'outline/business/presentation',
    activeIcon: 'fill/education/presentation',
  },
  {
    title: 'Cộng đồng',
    route: 'navigateCommunityView',
    icon: 'outline/user interface/f-chat',
    activeIcon: 'fill/user interface/f-chat',
  },
  // {
  //   title: 'Xếp hạng <PERSON>ku<PERSON>',
  //   route: 'navigateSakupiView',
  //   icon: 'outline/user interface/b-chart',
  //   activeIcon: 'fill/user interface/b-chart',
  // },
];

function CustomDrawerContent(props: any) {
  const { navigation, state } = props;
  // Set the initial selected route based on the active route
  const [selectedRoute, setSelectedRoute] = useState(() => {
    if (state.index === 0) return 'ESchoolParent';
    if (state.index === 1) return 'CommunityParent';
    if (state.index === 2) return 'SakupiParent';
    return 'ESchoolParent';
  });

  const handleNavigation = (item: any) => {
    let newRoute = 'ESchoolParent';

    if (item.route === 'navigateEComView') {
      newRoute = 'ESchoolParent';
    } else if (item.route === 'navigateCommunityView') {
      newRoute = 'CommunityParent';
    } else if (item.route === 'navigateSakupiView') {
      newRoute = 'SakupiParent';
    }

    setSelectedRoute(newRoute);

    if (item.route === 'navigateEComlView') {
      // Navigate to the main eSchool view
      if (state.index !== 0) {
        navigation.navigate(RootScreen.navigateEComParent);
      }
    } else if (item.route === 'navigateCommunityView') {
      // Navigate to the main Community view
      if (state.index !== 1) {
        navigation.navigate(RootScreen.navigateCommunityParent);
      }
    } else if (item.route === 'navigateSakupiView') {
      // Navigate to the Sakupi Ranking view
      if (state.index !== 2) {
        navigation.navigate(RootScreen.navigateSakupiParent);
      }
    }

    // Close drawer after a short delay to ensure navigation completes
    setTimeout(() => {
      navigation.closeDrawer();
    }, 100);
  };

  return (
    <View style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.header}>
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <Image
            source={require('../../../../assets/playstore.png')}
            style={styles.logo}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <Winicon src="outline/user interface/e-remove" size={24} />
        </TouchableOpacity>
      </SafeAreaView>

      <View style={{ paddingTop: 16, marginHorizontal: 16 }}>
        {menuItems.map((item, index) => (
          <ListTile
            key={index}
            style={{
              backgroundColor:
                (selectedRoute === 'ESchoolParent' &&
                  item.route === 'navigateEComView') ||
                  (selectedRoute === 'CommunityParent' &&
                    item.route === 'navigateCommunityView') ||
                  (selectedRoute === 'SakupiParent' &&
                    item.route === 'navigateSakupiView')
                  ? ColorThemes.light.neutral_main_background_color
                  : 'transparent',
            }}
            onPress={() => handleNavigation(item)}
            leading={
              <Winicon
                src={
                  (selectedRoute === 'ESchoolParent' &&
                    item.route === 'navigateEComView') ||
                    (selectedRoute === 'CommunityParent' &&
                      item.route === 'navigateCommunityView') ||
                    (selectedRoute === 'SakupiParent' &&
                      item.route === 'navigateSakupiView')
                    ? item.activeIcon
                    : item.icon
                }
                size={20}
                color={
                  (selectedRoute === 'ESchoolParent' &&
                    item.route === 'navigateEComView') ||
                    (selectedRoute === 'CommunityParent' &&
                      item.route === 'navigateCommunityView') ||
                    (selectedRoute === 'SakupiParent' &&
                      item.route === 'navigateSakupiView')
                    ? ColorThemes.light.primary_main_color
                    : undefined
                }
              />
            }
            title={
              <Text
                style={[
                  styles.menuText,
                  {
                    color:
                      (selectedRoute === 'ESchoolParent' &&
                        item.route === 'navigateEComView') ||
                        (selectedRoute === 'CommunityParent' &&
                          item.route === 'navigateCommunityView') ||
                        (selectedRoute === 'SakupiParent' &&
                          item.route === 'navigateSakupiView')
                        ? ColorThemes.light.primary_main_color
                        : ColorThemes.light.neutral_text_title_color,
                  },
                ]}>
                {item.title}
              </Text>
            }
          />
        ))}
      </View>
    </View>
  );
}

const Drawer = createDrawerNavigator();
const Stack = createNativeStackNavigator();

// Tạo một Stack Navigator riêng cho E-School
function EComParentNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={RootScreen.navigateEComView}
        component={EComStackNavigator}
      />
    </Stack.Navigator>
  );
}

// Tạo một Stack Navigator riêng cho Community
// function CommunityParentNavigator() {
//   return (
//     <Stack.Navigator screenOptions={{headerShown: false}}>
//       <Stack.Screen
//         name={RootScreen.navigateCommunityView}
//         component={CommunityNavigation}
//       />
//     </Stack.Navigator>
//   );
// }

// Tạo một Stack Navigator riêng cho Sakupi Ranking
// function SakupiParentNavigator() {
//   return (
//     <Stack.Navigator screenOptions={{headerShown: false}}>
//       <Stack.Screen
//         name={RootScreen.navigateSakupiView}
//         component={RankingSakupiIndex}
//       />
//     </Stack.Navigator>
//   );
// }

export function DrawerMain() {
  // Function to determine if drawer should be enabled based on route
  const getDrawerEnabledStatus = (route: any) => {
    const routeName = getFocusedRouteNameFromRoute(route) as string | undefined;

    if (route.name === RootScreen.navigateEComParent) {
      // Only enable drawer in main eCom view
      return !routeName || routeName === RootScreen.navigateEComView;
    } else if (route.name === RootScreen.navigateCommunityParent) {
      // Only enable drawer in main Community layout
      // return !routeName || routeName === RootScreen.CommunityLayout;
    } else if (route.name === RootScreen.navigateSakupiParent) {
      // Only enable drawer in main Sakupi Ranking view
      return !routeName || routeName === RootScreen.navigateSakupiView;
    }

    return false;
  };

  // List of all stack screens where drawer should be disabled
  const stackScreens = [
    // eSchool stack screens
    RootScreen.Instructors,
    RootScreen.order,
    RootScreen.Notification,
    RootScreen.SettingProfile,
    RootScreen.BiometricSetting,
    RootScreen.VnpayPaymentScreen,
    RootScreen.PurchaseHistory,
    RootScreen.login,
    RootScreen.splashView,
    RootScreen.NotifCommunity,
  ];

  return (
    <Drawer.Navigator
      initialRouteName={RootScreen.navigateEComParent}
      screenOptions={({ route }) => {
        // Get the current route name
        const routeName = getFocusedRouteNameFromRoute(route) as
          | string
          | undefined;

        // Check if we're in a stack screen
        const isStackScreen =
          routeName && stackScreens.includes(routeName as RootScreen);

        // Only enable drawer in main screens and disable in stack screens
        const isDrawerEnabled = !isStackScreen;

        return {
          headerShown: false,
          drawerType: 'front',
          drawerStyle: {
            width: '70%',
          },
          // Completely disable drawer gestures in stack screens
          // swipeEnabled: isDrawerEnabled,
          swipeEnabled: false,
          // Disable opening drawer with gesture
          drawerLockMode: isDrawerEnabled ? 'unlocked' : 'locked-closed',
        };
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Drawer.Screen
        name={RootScreen.navigateEComParent}
        component={EComParentNavigator}
        options={({ route }) => {
          const isDrawerEnabled = getDrawerEnabledStatus(route);

          return {
            title: 'E-Com',
            headerShown: false,
            drawerItemStyle: {
              display: isDrawerEnabled ? 'flex' : 'none',
            },
          };
        }}
      />
      {/* <Drawer.Screen
        name={RootScreen.navigateCommunityParent}
        component={CommunityParentNavigator}
        options={({route}) => {
          const isDrawerEnabled = getDrawerEnabledStatus(route);

          return {
            title: 'Community',
            headerShown: false,
            drawerItemStyle: {
              display: isDrawerEnabled ? 'flex' : 'none',
            },
          };
        }}
      /> */}
      {/* <Drawer.Screen
        name={RootScreen.navigateSakupiParent}
        component={SakupiParentNavigator}
        options={({route}) => {
          const isDrawerEnabled = getDrawerEnabledStatus(route);

          return {
            title: 'Sakupi Ranking',
            headerShown: false,
            drawerItemStyle: {
              display: isDrawerEnabled ? 'flex' : 'none',
            },
          };
        }}
      /> */}
    </Drawer.Navigator>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  logo: {
    resizeMode: 'contain',
    height: 56,
    aspectRatio: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 8,
    borderRadius: 8,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
});
