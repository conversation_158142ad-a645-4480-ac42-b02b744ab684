#!/usr/bin/env node

const https = require('https');

// API Keys from the project
const ANDROID_API_KEY = 'AIzaSyCKSVst_zCoLw2gC0zfbTvfuY_WUEaWFyE';
const IOS_API_KEY = 'AIzaSyDyq-nkW26hnSM__pUIoDFb2PfnirFzxgw';

function checkApiKey(apiKey, platform) {
  return new Promise((resolve, reject) => {
    const url = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          if (data.includes('Google Maps JavaScript API')) {
            resolve(`✅ ${platform} API Key is valid`);
          } else {
            resolve(`❌ ${platform} API Key may have restrictions`);
          }
        } else {
          resolve(`❌ ${platform} API Key failed with status: ${res.statusCode}`);
        }
      });
    }).on('error', (err) => {
      reject(`❌ ${platform} API Key check failed: ${err.message}`);
    });
  });
}

async function checkAllKeys() {
  console.log('🔍 Checking Google Maps API Keys...\n');
  
  try {
    const androidResult = await checkApiKey(ANDROID_API_KEY, 'Android');
    console.log(androidResult);
    
    const iosResult = await checkApiKey(IOS_API_KEY, 'iOS');
    console.log(iosResult);
    
    console.log('\n📋 Recommendations:');
    console.log('1. Make sure the API keys have the following APIs enabled:');
    console.log('   - Maps SDK for Android (for Android key)');
    console.log('   - Maps SDK for iOS (for iOS key)');
    console.log('   - Places API (if using places features)');
    console.log('   - Geocoding API (if using geocoding)');
    console.log('');
    console.log('2. Check API key restrictions:');
    console.log('   - Android key should be restricted to your app package name');
    console.log('   - iOS key should be restricted to your app bundle ID');
    console.log('');
    console.log('3. Verify billing is enabled for your Google Cloud project');
    
  } catch (error) {
    console.error('Error checking API keys:', error);
  }
}

checkAllKeys();
